package com.feidi.xx.cross.operate.dubbo;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.cache.system.SysDistrictCacheVo;
import com.feidi.xx.common.core.enums.SystemCacheKeyEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.cross.operate.api.RemoteCityService;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityInfoVo;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.operate.domain.OprCity;
import com.feidi.xx.cross.operate.domain.vo.OprCityVo;
import com.feidi.xx.cross.operate.mapper.OprCityMapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 城市dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/14
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteCityServiceImpl implements RemoteCityService {

    private final OprCityMapper cityMapper;

    /**
     * 根据cityCode查询城市信息
     */
    @Override
    public RemoteCityVo queryByCityCode(String cityCode) {
        OprCityVo oprCityVo = cityMapper.selectVoOne(new LambdaQueryWrapper<OprCity>().eq(OprCity::getCityCode, cityCode));
        return BeanUtils.copyProperties(oprCityVo, RemoteCityVo.class);
    }

    @Override
    public List<String> queryByCityIds(List<Long> cityIds) {
        if (CollectionUtils.isEmpty(cityIds)) {
            return Collections.emptyList();
        }
        List<OprCityVo> cityVos = cityMapper.selectVoList(new LambdaQueryWrapper<OprCity>().in(OprCity::getId, cityIds));
        return cityVos.stream().map(OprCityVo::getCityCode).map(code -> {
                    SysDistrictCacheVo value = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_CITY_CODE_KEY.create(), code);
                    return value != null ? value.getName() : null;
                }).filter(Objects::nonNull)
                // 过滤掉可能的 null 值
                .toList();
    }

    /**
     * 根据开城城市表id查询开城城市信息
     *
     * @param id 开城城市表id
     * @return 开城城市信息
     */
    @Override
    public RemoteCityVo queryById(Long id) {
        OprCity oprCity = cityMapper.selectById(id);
        return BeanUtils.copyProperties(oprCity, RemoteCityVo.class);
    }

    /**
     * 根据开城城市表id集合查询开城城市信息
     *
     * @param ids 开城城市表id
     * @return 开城城市信息集合
     */
    @Override
    public List<RemoteCityVo> queryByIds(List<Long> ids) {
        if (CollUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<OprCity> oprCities = cityMapper.selectBatchIds(ids);
        return BeanUtils.copyToList(oprCities, RemoteCityVo.class);
    }

    @Override
    public List<RemoteCityInfoVo> queryInfoByCityCode(List<String> cityCodes) {
        List<OprCityVo> oprCityVos = cityMapper.selectVoList(new LambdaQueryWrapper<OprCity>().in(OprCity::getCityCode, cityCodes));
        if (CollUtils.isEmpty(oprCityVos)) {
            return Collections.emptyList();
        }
        List<RemoteCityInfoVo> remoteCityInfoVos = BeanUtils.copyToList(oprCityVos, RemoteCityInfoVo.class);
        remoteCityInfoVos.parallelStream().forEach(vo -> {
            SysDistrictCacheVo sysDistrictCacheVo = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_CITY_CODE_KEY.create(), vo.getCityCode());
            if (sysDistrictCacheVo != null) {
                vo.setCity(sysDistrictCacheVo.getName());
            }
            SysDistrictCacheVo provinceCacheVo = RedisUtils.getCacheMapValue(SystemCacheKeyEnum.SYS_ID_KEY.create(), vo.getProvinceId() + "");
            if (provinceCacheVo != null) {
                vo.setProvince(provinceCacheVo.getName());
            }
        });
        return remoteCityInfoVos;
    }
}
