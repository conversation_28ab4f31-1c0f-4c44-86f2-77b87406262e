package com.feidi.xx.cross.common.cache.market.enums;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.feidi.xx.cross.common.cache.market.constants.MarketCacheConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 营销缓存key拼装枚举
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum MktCacheKeyEnum {

    /**
     * 代客下单次数枚举
     */
    MKT_AGENT_ORDER_LIMIT_KEY(MarketCacheConstants.AGENT_ORDER_LIMIT_KEY, 60 * 60 * 24, 2, "见静态常量定义，参数1：用户类型 参数2：用户id "),

    /**
     * 代客下单绑定乘客关系
     */
    MKT_AGENT_ORDER_BIND_PASSENGER_KEY(MarketCacheConstants.AGENT_ORDER_BIND_PASSENGER_KEY, 60 * 60 * 24, 3, "见静态常量定义，参数1：用户类型 参数2：用户id 参数3:乘客id "),

    /**
     * 邀请有奖配置
     */
    MKT_INVITE_CONFIG_KEY(MarketCacheConstants.INVITE_CONFIG_KEY, 60 * 60 * 24 * 30, 1, "见静态常量定义，参数1：代理商id"),
    /**
     * 优惠券库存
     */
    MKT_COUPON_STOCK_KEY(MarketCacheConstants.COUPON_STOCK_KEY, 60 * 60 * 24 * 30, 1, "见静态常量定义，参数1：优惠卷模版id"),

    /**
     * 优惠券最多领取几张
     */
    MKT_COUPON_USER_COUNT_KEY(MarketCacheConstants.COUPON_USER_COUNT_KEY, 60 * 60 * 24 * 30, 1, "见静态常量定义，参数1：优惠卷模版id"),
    /**
     * 优惠券用户领取次数
     */
    MKT_COUPON_USER_KEY(MarketCacheConstants.COUPON_USER_KEY, 60 * 60 * 24 * 30, 2, "见静态常量定义，参数1：优惠卷模版id 参数2:用户id"),

    /**
     * 优惠卷库存锁
     */
    MKT_COUPON_STOCK_LOCK_KEY(MarketCacheConstants.COUPON_STOCK_LOCK_KEY, 10, 1, "见静态常量定义，参数1：优惠卷模版id"),
    /**
     * 定向放券，文件上传缓存
     */
    MKT_TARGETED_COUPONS_KEY(MarketCacheConstants.TARGETED_COUPONS_KEY, 60 * 60 * 24, 1, "一个参数"),

    /**
     * 邀请码缓存
     */
    INVITE_CODE_TEMP_KEY(MarketCacheConstants.INVITE_CODE_TEMP_KEY, 60 * 60 * 24, 3, "三个参数，活动id,用户id,城市code"),
    ;
    /**
     * 前缀
     */
    private final String prefix;

    /**
     * 过期时间（单位：秒）
     */
    private final int duration;

    /**
     * 参数个数
     */
    private final int argsNum;

    /**
     * 描述 需要描述每一个参数都应该传什么
     */
    private final String desc;

    /**
     * 分隔符
     */
    private static final String SEPARATOR = ":";

    static {
        Set<String> set = Arrays.stream(values()).map(MktCacheKeyEnum::getPrefix).collect(Collectors.toSet());
        Assert.isTrue(values().length == set.size(), "存在部分枚举的prefix重复，无法初始化");
    }

    public String create(Object... args) {
        if (argsNum == 0) {
            return prefix;
        }
        Assert.isTrue(args.length == argsNum, "参数个数不正确");
        return prefix + StrUtil.join(SEPARATOR, args);
    }

    /**
     * 获取过期时间
     * 在当前过期时间的基础上，浮动0%-3%，向下取整
     * 小于六十秒时，直接返回，不进行浮动
     *
     * @return 过期时间
     */
    public Duration getDuration() {
        if (duration < 60) {
            return Duration.ofSeconds(duration);
        }

        ThreadLocalRandom random = ThreadLocalRandom.current();
        // 生成随机浮动（1.00-1.03范围）
        double factor = 1 + random.nextDouble(0.03);
        int floatedTime = (int) (duration * factor);
        // 确保结果不小于1（避免归零）
        int max = Math.max(floatedTime, 1);

        return Duration.ofSeconds(max);
    }
}
