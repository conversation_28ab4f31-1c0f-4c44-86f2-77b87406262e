package com.feidi.xx.cross.order;

import cn.hutool.core.img.ImgUtil;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

@SpringBootTest(classes = XXCrossOrderApplication.class)
public class MainTest {
    @Resource
    private OrdOrderMapper ordOrderMapper;

    @Test
    public void testQueryOrderCountByDriverIdsAndStatuses() {
        // 准备测试数据
        // 调用被测试方法
//        List<QueryCountBo> result = ordOrderMapper.queryOrderCountByDriverIdsAndStatuses(List.of(1917203486418591746L, 1917203576323497986L), List.of());
//        System.out.println(result);
    }

    public static void main(String[] args) {
        File s = new File("C:\\Users\\<USER>\\Downloads\\MVIMG_20250812_160755.jpg");
        File t = new File("C:\\Users\\<USER>\\Downloads\\2.jpg");
        ImgUtil.scale(s, t, 2f);
    }
}
