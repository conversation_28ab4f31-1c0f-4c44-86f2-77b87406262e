package com.feidi.xx.cross.order.chain.payment;


import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderPaymentBo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderPaymentChainContext extends OrderBaseChainContext {

    /**
     * appid
     */
    private String appId;

    /**
     * 微信openId
     */
    @NotBlank(message = "openId不能为空")
    private String openId;

    /**
     * 支付方式：1.微信；2.支付宝（只有代客下单支付、才支持支付宝扫码付款）
     * {@link com.feidi.xx.common.core.enums.PaymentTypeEnum}
     */
    private String paymentType;

    /**
     * 是否代客下单支付:Y.是（生成二维码）；N.直接JS支付
     */
    private String isReplace;

    /**
     * 订单金额[基础金额+附加费金额-客诉扣款金额]
     */
    private Long orderPrice;

    /**
     * 实际支付金额
     */
    private Long payPrice;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 小程序码
     */
    private String xcxCode;

    private RemoteOrderPaymentBo handleBo;

    public OrderPaymentChainContext() {
        this.setOperateType(OperateTypeEnum.PAYMENT.getCode());
    }

}
