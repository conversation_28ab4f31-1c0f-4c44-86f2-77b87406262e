package com.feidi.xx.cross.order.service;

import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderCommentBo;
import com.feidi.xx.cross.order.domain.bo.OrdUpdateRemarkBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPaymentBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPlaceBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrListVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrPublicVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrVo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public interface IOrdOrderMbrService {

    /**
     * 获取订单详情
     *
     * @param id
     * @return
     */
    OrdOrderMbrVo queryById(Long id);

    /**
     * 获取订单详情
     *
     * @param code
     * @return
     */
    OrdOrderMbrVo queryByCode(String code);

    /**
     * 获取订单列表
     *
     * @param bo
     * @return
     */
    TableDataInfo<OrdOrderMbrListVo> queryPageList(OrdOrderQueryWebBo bo);

    /**
     * 获取最近的订单(下单页面)
     *
     * @return
     */
    List<OrdOrderMbrListVo> queryLast(OrdOrderQueryWebBo bo);

    /**
     * 获取最近订单数量(不包括取消和已完成)
     */
    Integer lastNum(OrdOrderQueryWebBo bo);

    /**
     * 下单
     *
     * @param bo
     * @return
     */
    Long place(OrdOrderPlaceBo bo);

    Boolean cancelBo(OrdOrderCancelBo bo);

    Boolean tripStart(OrdOrderHandleBo bo);

    Boolean tripEnd(OrdOrderHandleBo bo);

    /**
     * 发起支付
     *
     * @param bo
     * @return
     */
    Object payment(OrdOrderPaymentBo bo);

    /**
     * 刷新支付状态
     *
     * @param orderId
     */
    void refreshPayment(Long orderId);

    /**
     * 常用位置
     *
     * @return
     */
    List<OrdPositionVo> commonPosition();

    /**
     * 获取司机位置
     *
     * @param driverId
     * @return
     */
    OrdOrderTrackLocationCacheVo driverPosition(Long driverId);

    /**
     * 订单转卖
     *
     * @param bo 订单转卖参数
     * @return 订单ID
     */
    Long resell(OrdOrderPlaceBo bo);

    /**
     * 修改备注
     *
     * @param bo
     */
    void updateRemark(OrdUpdateRemarkBo bo);

    /**
     * 评价订单
     *
     * @param bo
     */
    void comment(OrdOrderCommentBo bo);

    /**
     * 乘客端订单公开接口
     *
     * @param id
     * @return
     */
    OrdOrderMbrPublicVo publicInfo(@NotNull(message = "主键不能为空") Long id);
}

