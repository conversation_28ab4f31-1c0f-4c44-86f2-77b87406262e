package com.feidi.xx.cross.order.chain.handler;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.http.ContentType;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.oss.entity.UploadResult;
import com.feidi.xx.common.oss.factory.OssFactory;
import com.feidi.xx.common.payment.config.AliPayConfig;
import com.feidi.xx.common.payment.config.PayConfig;
import com.feidi.xx.common.payment.config.WxPayConfig;
import com.feidi.xx.common.payment.domain.payment.bo.PayBo;
import com.feidi.xx.common.payment.domain.payment.vo.PayResultVo;
import com.feidi.xx.common.payment.domain.payment.vo.PrePayResultVo;
import com.feidi.xx.common.payment.listener.PaymentConfigManager;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.payment.strategy.IPaymentService;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.RemotePaymentRecordService;
import com.feidi.xx.cross.finance.api.domain.bo.RemotePaymentRecordBo;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.operate.api.RemoteEstimateRecordService;
import com.feidi.xx.cross.operate.api.domain.estimate.vo.RemoteEstimateRecordVo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainResult;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import com.feidi.xx.cross.order.chain.common.ChainContextUtil;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainContext;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainResult;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainContext;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainResult;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainResult;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderBo;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.helper.OrdOrderComplainHelper;
import com.feidi.xx.cross.order.helper.OrdOrderProcessHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.mq.producer.OrderCancelProducer;
import com.feidi.xx.cross.order.service.IOrdOrderRebateService;
import com.feidi.xx.cross.order.service.IOrdOrderService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 订单业务处理器，处理业务逻辑，涉及数据库相关操作
 * 注意需要是多例模式
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderServiceHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {

    // 微信小程序
    private final WxMaService wxMaService;
    private final OrdOrderMapper baseMapper;
    private final OrdOrderInfoMapper infoMapper;
    private final IPaymentService paymentService;
    private final PowCacheManager powCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final OrderCancelProducer orderCancelProducer;
    private final IOrdOrderService orderService;
    private final IOrdOrderRebateService orderOrderRebateService;
    private final OrdOrderProcessHelper ordOrderProcessHelper;
    private final OrdOrderComplainHelper ordOrderComplainHelper;
    private final OrdOrderOperateProducer operateProducer;
    private final MktCacheManager mktCacheManager;
    private final ScheduledExecutorService scheduledExecutorService;

    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;
    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;
    @DubboReference
    private final RemotePaymentRecordService remotePaymentRecordService;
    @DubboReference
    private final RemoteEstimateRecordService remoteEstimateRecordService;


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void handle(T context) {
        OrdOrderOperateEvent operateEvent = context.getOperateEvent();
        boolean success = true;
        try {
            if (context instanceof OrderPlaceChainContext placeContext) {
                RemoteOrderBo remoteOrderBo = placeContext.getOrderBo();
                OrdOrderBo ordOrderBo = BeanUtil.copyProperties(remoteOrderBo, OrdOrderBo.class);
                ordOrderBo.setStartPosition(BeanUtils.copyProperties(remoteOrderBo.getStartPosition(), OrdPositionBo.class));
                ordOrderBo.setEndPosition(BeanUtils.copyProperties(remoteOrderBo.getEndPosition(), OrdPositionBo.class));
                //订单 新增询价id
                RemoteEstimateRecordVo estimateRecord = remoteEstimateRecordService.getEstimateRecordByEstimateKey(placeContext.getEstimateKey());
                ordOrderBo.setEstimateId(estimateRecord.getId());
                OrdOrder order = orderService.insertByBo(ordOrderBo);
                Assert.notNull(order, "下单异常，请联系客服");
                // 更新订单ID
                placeContext.setOrderId(order.getId());
                operateEvent.setOrderId(order.getId());
                //询价 新增订单id
                remoteEstimateRecordService.updateEstimateRecordById(estimateRecord.getId(), order.getId());

                // 超时
                orderCancelProducer.sendPaymentCancelMessage(order.getId(), order.getCreateTime());
                //orderCancelProducer.sendReceiveCancelMessage(order.getId(), order.getLatestTime());

                OrderPlaceChainResult result = new OrderPlaceChainResult(success);
                result.setId(order.getId());
                ChainContextUtil.setResult(result);
            } else if (context instanceof OrderOperateChainContext operateContext) {
                OrdOrder order = operateContext.getOrder();
                if (OrderStatusEnum.ING.getCode().equals(operateContext.getStatus())) {
                    operateContext.setOperateType(OperateTypeEnum.INTO.getCode());
                    // 修改订单状态
                    Date now = DateUtils.getNowDate();
                    LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(OrdOrder::getId, order.getId())
                            .eq(OrdOrder::getVersion, order.getVersion())
                            .set(OrdOrder::getVersion, order.getVersion() + 1)
                            .set(OrdOrder::getStatus, operateContext.getStatus())
                            .set(OrdOrder::getTripStartTime, now)
                            .set(OrdOrder::getUpdateBy, LoginHelper.getUserId())
                            .set(OrdOrder::getUpdateTime, now);
                    success &= baseMapper.update(updateWrapper) > 0;
                    Assert.isTrue(success, "操作异常，请联系客服");
                } else if (OrderStatusEnum.FINISH.getCode().equals(operateContext.getStatus())) {
                    operateContext.setOperateType(OperateTypeEnum.FINISH.getCode());
                    // 计算服务时长
                    long duration = ordOrderProcessHelper.calculateOrderServiceTime(order.getTripStartTime());
                    Date now = DateUtils.getNowDate();
                    // 修改订单关联信息
                    LambdaUpdateWrapper<OrdOrderInfo> infoUpdateWrapper = new LambdaUpdateWrapper<>();
                    infoUpdateWrapper.eq(OrdOrderInfo::getOrderId, operateContext.getOrderId())
                            .set(OrdOrderInfo::getDuration, duration)
                            .set(OrdOrderInfo::getUpdateTime, now);
                    success &= infoMapper.update(infoUpdateWrapper) > 0;

                    // 修改订单信息
                    LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(OrdOrder::getId, operateContext.getOrder().getId())
                            .eq(OrdOrder::getVersion, operateContext.getOrder().getVersion())
                            .set(OrdOrder::getVersion, operateContext.getOrder().getVersion() + 1)
                            .set(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                            .set(OrdOrder::getFinishTime, now)
                            .set(OrdOrder::getRebateStatus, RebateStatusEnum.ING.getCode())
                            .set(OrdOrder::getResellRebateStatus, RebateStatusEnum.ING.getCode())
                            .set(OrdOrder::getUpdateBy, LoginHelper.getUserId())
                            .set(OrdOrder::getUpdateTime, now);
                    success &= baseMapper.update(updateWrapper) > 0;
                    RemoteRebateBo rebateBo = orderOrderRebateService.createRemoteRebateVo(order);
                    success &= remoteDrvWalletService.orderFreezeAdd(rebateBo);

                    // 订单转卖返利
                    if (Objects.equals(order.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())
                            && order.getResellDriverId() != null && order.getResellDriverId() > 0) {
                        rebateBo.setResellDriverId(order.getResellDriverId());
                        RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(order.getResellDriverId());
                        // 卖单司机收益（订单金额 - 转卖后司机接单金额 - 订单金额 * 订单转卖服务费比例）
                        long resellDriverProfit = order.getOrderPrice() -  order.getResellDriverPrice() - ArithUtils.profitUseBigDecimal(order.getOrderPrice(), driverVo.getResellServiceRate());
                        rebateBo.setResellDriverProfit(resellDriverProfit);
                        rebateBo.setResellDriverName(driverVo.getName());
                        rebateBo.setResellDriverPhone(driverVo.getPhone());
                        remoteDrvWalletService.orderFreezeAddForResell(rebateBo);
                    }

                    Assert.isTrue(success, "完单异常，请联系客服");
                    // 完单后 代客下单修改绑定关系
                    if (Objects.equals(order.getCreateModel(), CreateModelEnum.AGENT_ORDER.getCode())) {
                        log.info("代理商代客下单");
                        // 乘客代下单
                        if (mktCacheManager.getPassengerId(UserTypeEnum.AGENT_USER.getUserType(), order.getAgentId(), order.getPassengerId())) {
                            //异步
                            scheduledExecutorService.schedule(() -> {
                                remotePassengerService.bindPassenger(UserTypeEnum.AGENT_USER.getUserType(), order.getAgentId(),null, order.getPassengerId());
                            }, 0, TimeUnit.SECONDS);
                        }
                    }else if (Objects.equals(order.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
                        log.info("司机代下单");
                        // 乘客扫码下单
                        if (mktCacheManager.getPassengerId(UserTypeEnum.DRIVER_USER.getUserType(), order.getDriverId(), order.getPassengerId())) {
                            //异步
                            scheduledExecutorService.schedule(() -> {
                                remotePassengerService.bindPassenger(UserTypeEnum.DRIVER_USER.getUserType(),order.getAgentId(), order.getDriverId(), order.getPassengerId());
                            }, 0, TimeUnit.SECONDS);
                        }
                    }
                }
                ChainContextUtil.setResult(new OrderOperateChainResult(success));
            } else if (context instanceof OrderCancelChainContext cancelContext) {
                Date now = DateUtils.getNowDate();
                OrdOrder order = cancelContext.getOrder();
                RemoteOrderCancelBo cancelBo = cancelContext.getHandleBo();

                // 修改订单状态
                LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(OrdOrder::getId, order.getId())
                        // 保证同一时刻内 取消订单状态不会被其他操作覆盖
                        .eq(OrdOrder::getVersion, order.getVersion())
                        .set(OrdOrder::getVersion, order.getVersion() + 1)
                        .set(OrdOrder::getStatus, OrderStatusEnum.CANCEL.getCode())
                        .set(OrdOrder::getCancelUserType, cancelBo.getUserType())
                        .set(OrdOrder::getCancelType, cancelBo.getCancelType())
                        .set(OrdOrder::getCancelTime, now)
                        .set(OrdOrder::getUpdateBy, LoginHelper.getUserId())
                        .set(OrdOrder::getUpdateTime, now);

                // 支付中的更新支付状态为未支付
                if (PaymentStatusEnum.ING.getCode().equals(order.getCancelType())) {
                    updateWrapper.set(OrdOrder::getPayStatus, PaymentStatusEnum.NO.getCode());
                }

                // 修改订单关联表信息
                LambdaUpdateWrapper<OrdOrderInfo> infoUpdateWrapper = new LambdaUpdateWrapper<>();
                infoUpdateWrapper.eq(OrdOrderInfo::getOrderId, order.getId())
                        .set(OrdOrderInfo::getCancelUserId, cancelBo.getUserId())
                        .set(OrdOrderInfo::getCancelRemark, cancelBo.getCancelRemark())
                        .set(OrdOrderInfo::getUpdateBy, LoginHelper.getUserId())
                        .set(OrdOrderInfo::getUpdateTime, now);

                // 订单已完单，在进行取消操作 - 视为客诉
                if (Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode())) {
                    // 完单后客诉，不再修改订单状态
                    updateWrapper.set(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode());

                    cancelBo.setComplain(IsYesEnum.YES.getCode());
                    cancelBo.setComplainPrice(order.getOrderPrice());
                    cancelBo.setComplainType(ComplainTypeEnum.OTHER.getCode());
                    cancelBo.setComplainRemark(PlatformCodeEnum.getInfoByCode(cancelBo.getPlatformCode()) + "客诉");
                    cancelBo.setComplainTime(now);
                    success &= ordOrderComplainHelper.complain(BeanUtils.copyProperties(cancelBo, RemoteOrderCancelBo.class), order);

                    // 退款
                    if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
                        // 如果自营的订单看是否有邀请有奖的账单
                        RemoteOrderRateVo remoteOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_AGENT.getCode());
                        if (ObjectUtils.isNotNull(remoteOrderRateVo)) {
                            Long userId = remoteOrderRateVo.getUserId();
                            //更新代理商流水类型
                            success &= remoteInviteRecordService.updateRebateStatus(userId, remoteOrderRateVo.getOrderId());
                        }
                    }
                } else {
                    // 返利状态 - 取消
                    updateWrapper.set(OrdOrder::getRebateStatus, RebateStatusEnum.CANCEL.getCode());

                    // 自营平台订单取消后，需要把乘客已使用的优惠券返还（客诉不需要返还）
                    if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
                        ordOrderProcessHelper.updateCouponGrantStatus(order, CouponStatusEnum.NOT_USED.getCode());
                    }
                }
                // 先退钱再更新订单信息，退钱不成功不处理
                if (ordOrderProcessHelper.refund(order)) {
                    success &= baseMapper.update(updateWrapper) > 0;
                    success &= infoMapper.update(infoUpdateWrapper) > 0;
                } else {
                    throw new ServiceException("订单退款失败，请联系管理员");
                }
                Assert.isTrue(success, "取消异常，请联系客服");

                // 结果
                ChainContextUtil.setResult(new OrderCancelChainResult(success));
            } else if (context instanceof OrderPaymentChainContext paymentContext) {
                log.info("支付参数：【{}】", JsonUtils.toJsonString(paymentContext));
                if (StringUtils.isBlank(paymentContext.getTenantId())) {
                    log.info("2支付paymentContext中tenantId为空，使用默认值");
                    paymentContext.setTenantId(Constants.TENANT_ID);
                }
                PaymentRecordEvent event = createPaymentEvent(paymentContext);
                try {
                    String isReplace = paymentContext.getIsReplace();
                    String paymentType = paymentContext.getPaymentType();
                    Long payPrice = paymentContext.getPayPrice();

                    PayBo payBo = new PayBo(PaymentChannelEnum.getByType(paymentContext.getPaymentType()));
                    payBo.setAppId(paymentContext.getAppId());
                    payBo.setTenantId(paymentContext.getTenantId());
                    if (StringUtils.isBlank(payBo.getTenantId())) {
                        log.info("支付payBo中tenantId为空，使用默认值");
                        payBo.setTenantId(Constants.TENANT_ID);
                    }
                    payBo.setBizNo(event.getOutBizNo());
                    payBo.setTotalAmount(payPrice);

                    Object payInfo = null;
                    if (ObjectUtils.isNotNull(isReplace) && isReplace.equals(IsYesEnum.YES.getCode())) {
                        log.info("支付payBo：【{}】", JsonUtils.toJsonString(payBo));
                        PrePayResultVo prePayResultVo = paymentService.paymentCode(payBo);

                        BufferedImage image = QrCodeUtil.generate(prePayResultVo.getCodeUrl(), 200, 200);
                        ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
                        ImageIO.write(image, "png", byteStream);
                        String fileName = UUID.randomUUID().toString() + System.currentTimeMillis() + ".png";
                        UploadResult result = OssFactory.instanceOfPublic().upload(IoUtil.toStream(byteStream.toByteArray()), fileName, Long.valueOf(byteStream.size()), ContentType.OCTET_STREAM.getValue());
                        prePayResultVo.setCodeUrl(result.getUrl());

                        event.setResponseJson(JsonUtils.toJsonString(prePayResultVo));
                        payInfo = prePayResultVo;
                    } else {
                        if (paymentType.equals(PaymentTypeEnum.WX_PAY.getCode())) {
                            if (Objects.equals(LoginHelper.getUserType().getUserType(), UserTypeEnum.PASSENGER_USER.getUserType())) {
                                if (StringUtils.isBlank(paymentContext.getOpenId())) {
                                    log.info("openId：【{}】", "openId为空，尝试从用户凭证表中获取");
                                    String openId = remotePassengerService.getOpenId(LoginHelper.getUserId());
                                    paymentContext.setOpenId(openId);
                                }

                                if (StringUtils.isBlank(paymentContext.getOpenId()) && StringUtils.isNotBlank(paymentContext.getXcxCode())) {
                                    log.info("xcxCode：【{}】", paymentContext.getXcxCode());
                                    WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(paymentContext.getXcxCode());
                                    log.info("sessionInfo：【{}】", JsonUtils.toJsonString(sessionInfo));
                                    if (Objects.nonNull(sessionInfo)) {
                                        paymentContext.setOpenId(sessionInfo.getOpenid());
                                    }
                                }
                            }

                            Assert.notNull(paymentContext.getAppId(), "appid不能为空");
                            Assert.notNull(paymentContext.getOpenId(), "openid不能为空");
                            payBo.setOpenId(paymentContext.getOpenId());
                        }
                        PayResultVo payResultVo = paymentService.paymentJsapi(payBo);
                        event.setResponseJson(JsonUtils.toJsonString(payResultVo));
                        payInfo = payResultVo;
                    }
                    OrderPaymentChainResult result = new OrderPaymentChainResult(success);
                    result.setPayInfo(payInfo);
                    ChainContextUtil.setResult(result);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    throw new ServiceException("支付异常，请重试");
                } finally {
                    // 更新订单状态
                    paymentIng(paymentContext.getOrderId(), paymentContext.getPaymentType(), event.getOutBizNo());
                    // 记录支付流水
//                    PaymentRecordProducer.sendMessage(event);
                    RemotePaymentRecordBo convert = BeanUtils.copyProperties(event, RemotePaymentRecordBo.class);
                    remotePaymentRecordService.savePaymentRecord(convert);
                }
            }
        } catch (Exception e) {
            log.error("订单处理异常", e);
            success = false;
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setResponseJson(Arrays.toString(e.getStackTrace()));
            throw e;
        } finally {
            context.setSuccess(success);
            operateEvent.setOperateType(context.getOperateType());
            operateEvent.setResponseJson(JsonUtils.toJsonString(ChainContextUtil.getResult()));
            log.info("订单操作日志-1：【{}】", JsonUtils.toJsonString(operateEvent));
            operateProducer.sendMessage(operateEvent);
        }
    }

    private PaymentRecordEvent createPaymentEvent(OrderPaymentChainContext context) {
        PaymentRecordEvent event = new PaymentRecordEvent();
        event.setTenantId(context.getTenantId());

        PaymentChannelEnum channel = PaymentChannelEnum.getByType(context.getPaymentType());
        // 使用当前租户的主账号收款
        PayConfig payConfig = PaymentConfigManager.loadConfig(context.getTenantId(), channel.getCode(), context.getAppId(), PaymentDirectionEnum.INCOMING.getCode(), null);
        if (payConfig != null) {
            if (payConfig instanceof AliPayConfig aliPayConfig) {
                event.setAppId(aliPayConfig.getAppId());
            } else if (payConfig instanceof WxPayConfig wxPayConfig) {
                event.setAppId(wxPayConfig.getAppId());
                event.setMchId(wxPayConfig.getMchId());
            }
        }

        event.setJoinTable(JoinEnum.ORDER.getCode());
        event.setJoinId(context.getOrderId());
        event.setJoinNo(context.getOrderNo());
        event.setAmount(context.getPayPrice());
        event.setPaymentType(context.getPaymentType());

        event.setOutBizNo(PaymentRecordEvent.generateOutBizNo());
        event.setType(RecordTypeEnum.PASSENGER_PAYMENT.getCode());
        event.setStatus(PaymentStatusEnum.ING.getCode());
        event.setDirection(DirectionEnum.IN.getCode());
        return event;
    }

    private void paymentIng(Long id, String paymentType, String payNo) {
        Date time = new Date();
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, id)
                .set(OrdOrder::getPayStatus, PaymentStatusEnum.ING.getCode())
                .set(OrdOrder::getPayMode, paymentType)
                .set(OrdOrder::getPayNo, payNo)
                .set(OrdOrder::getPayTime, time);
        baseMapper.update(updateWrapper);
        // 超时更新为未支付
        ExceptionUtil.ignoreEx(() -> {
            orderCancelProducer.sendPaymentTypeChangeMessage(id, time);

        });
    }

}
