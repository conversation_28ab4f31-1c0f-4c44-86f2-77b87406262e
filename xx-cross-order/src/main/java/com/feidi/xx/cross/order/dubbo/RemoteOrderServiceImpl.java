package com.feidi.xx.cross.order.dubbo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.domain.SimpleAddress;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.PaymentStatusEnum;
import com.feidi.xx.common.core.enums.StartEndEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.cross.common.domain.QueryCountBo;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderHandleBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderPaymentBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdPosition;
import com.feidi.xx.cross.order.helper.OrdOrderProcessHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.mapper.OrdPositionMapper;
import com.feidi.xx.cross.order.strategy.OrdOrderStrategy;
import com.feidi.xx.cross.order.strategy.OrdOrderStrategyFactory;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单服务dubbo服务实现类
 *
 * <AUTHOR>
 * @date 2025/3/15
 */
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteOrderServiceImpl implements RemoteOrderService {

    private static final Logger log = LoggerFactory.getLogger(RemoteOrderServiceImpl.class);
    private final OrdOrderMapper ordOrderMapper;
    private final OrdPositionMapper positionMapper;
    private final OrdOrderProcessHelper ordOrderProcessHelper;
    private final OrdOrderStrategyFactory orderStrategyFactory;

    @Override
    public List<RemoteOrderVo> listByIds(Collection<Long> ids) {

        List<OrdPosition> positions = positionMapper.listByOrderIdsParallel(new ArrayList<>(ids), true);
        Map<Long, List<OrdPosition>> posMap = positions.stream()
                .collect(Collectors.groupingBy(OrdPosition::getOrderId));
        List<OrdOrder> orders = ordOrderMapper.listByIdsParallel(new ArrayList<>(ids));
        return orders.stream().map(order -> {
            RemoteOrderVo vo = new RemoteOrderVo();
            BeanUtil.copyProperties(order, vo);
            List<OrdPosition> posList = posMap.getOrDefault(order.getId(), Collections.emptyList());
            Map<String, OrdPosition> typeMap = posList.stream()
                    .collect(Collectors.toMap(OrdPosition::getType, Function.identity()));
            vo.setStart(Optional.ofNullable(typeMap.get(StartEndEnum.START.getCode()))
                    .map(OrdPosition::toSimpleAddress)
                    .orElseGet(SimpleAddress::new));
            vo.setEnd(Optional.ofNullable(typeMap.get(StartEndEnum.END.getCode()))
                    .map(OrdPosition::toSimpleAddress)
                    .orElseGet(SimpleAddress::new));
            return vo;
        }).collect(Collectors.toList());
    }


    @Override
    public Long waitProfit(Long driverId) {
        return 0L;
    }

    /**
     * 下单
     *
     * @param remoteOrderBo 下单参数
     * @return 下单结果
     */
    @Override
    public RemoteOrderVo placeOrder(RemoteOrderBo remoteOrderBo) {
        OrdOrderStrategy orderStrategy = orderStrategyFactory.getOrderStrategy(remoteOrderBo.getPlatformCode());
        OrdOrder order = orderStrategy.postOrder(remoteOrderBo);

        return BeanUtils.copyProperties(order, RemoteOrderVo.class);
    }

    /**
     * 美团通知派单
     *
     * @param handleBo 通知派单参数
     * @return 通知派单结果
     */
    @Override
    public Boolean confirmNotify(RemoteOrderHandleBo handleBo) {
        OrdOrderStrategy orderStrategy = orderStrategyFactory.getOrderStrategy(handleBo.getPlatformCode());
        return orderStrategy.confirmNotify(handleBo);
    }

    /**
     * 订单取消费
     *
     * @param handleBo 订单取消费参数
     * @return 订单取消费
     */
    @Override
    public Long orderCancelFee(RemoteOrderHandleBo handleBo) {
        OrdOrderStrategy orderStrategy = orderStrategyFactory.getOrderStrategy(handleBo.getPlatformCode());
        return orderStrategy.orderCancelFee(handleBo);
    }

    /**
     * 取消订单
     *
     * @param handleBo 取消订单参数
     * @return 取消订单结果
     */
    @Override
    public boolean cancelOrder(RemoteOrderHandleBo handleBo) {
        OrdOrderStrategy orderStrategy = orderStrategyFactory.getOrderStrategy(handleBo.getPlatformCode());
        return orderStrategy.cancelOrder(BeanUtil.copyProperties(handleBo, RemoteOrderCancelBo.class));
    }

    /**
     * 行程开始
     *
     * @param handleBo 行程开始
     * @return 行程开始结果
     */
    @Override
    public Boolean tripStart(RemoteOrderHandleBo handleBo) {
        OrdOrderStrategy orderStrategy = orderStrategyFactory.getOrderStrategy(handleBo.getPlatformCode());
        return orderStrategy.tripStart(handleBo);
    }

    /**
     * 行程结束
     *
     * @param handleBo 行程结束参数
     * @return 行程结束结果
     */
    @Override
    public Boolean tripEnd(RemoteOrderHandleBo handleBo) {
        OrdOrderStrategy orderStrategy = orderStrategyFactory.getOrderStrategy(handleBo.getPlatformCode());
        return orderStrategy.tripEnd(handleBo);
    }

    /**
     * 根据平台编号和平台单号查询订单信息
     *
     * @param platformCode 平台编号
     * @param platformNo   平台单号
     * @return 订单信息
     */
    @Override
    public RemoteOrderVo queryByPlatformCodeAndPlatformNo(String platformCode, String platformNo) {
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdOrder::getPlatformCode, platformCode)
                .eq(OrdOrder::getPlatformNo, platformNo)
                .orderByDesc(OrdOrder::getCreateTime)
                .last(Constants.LIMIT_ONE);

        OrdOrder order = ordOrderMapper.selectOne(lqw);

        return BeanUtils.copyProperties(order, RemoteOrderVo.class);
    }

    @Override
    public RemoteOrderDetailVo queryById(Long orderId) {
        OrdOrder ordOrder = ordOrderMapper.selectById(orderId);
        return BeanUtils.copyProperties(ordOrder, RemoteOrderDetailVo.class);
    }

    @Override
    public List<RemoteOrderVo> queryByDriverIdAndStatuses(Long driverId, List<String> orderStatuses) {
        return List.of();
    }

    @Override
    public List<RemoteOrderDetailVo> updateOrderFlowStatus(Long driverId, String newType, String oldType) {
        return List.of();
    }

    /**
     * 更新订单信息
     *
     * @return
     */
    @Override
    public Boolean updateOrderInsure(Long orderId, String insureNo) {
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, orderId)
                .set(StringUtils.isNotBlank(insureNo), OrdOrder::getInsureNo, insureNo)
                .set(OrdOrder::getUpdateTime, new Date());
        return ordOrderMapper.update(updateWrapper) > 0;
    }

    @Override
    public List<RemoteOrderVo> queryNotFinishOrderByDriverId(Long driverId) {
        List<OrdOrder> orders = ordOrderMapper.selectList(
                Wrappers.lambdaQuery(OrdOrder.class)
                        .eq(OrdOrder::getDriverId, driverId)
                        .in(OrdOrder::getStatus, OrderStatusEnum.CREATE.getCode(), OrderStatusEnum.RECEIVE.getCode(), OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode())
        );
        //获取订单id
        List<RemoteOrderVo> remoteOrderVos = BeanUtils.copyToList(orders, RemoteOrderVo.class);
        return remoteOrderVos;
    }

    /**
     * 根据订单编号查询订单信息
     *
     * @param orderNo 订单编号
     * @return 订单信息
     */
    @Override
    public RemoteOrderVo queryByOrderNo(String orderNo) {
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(OrdOrder::getOrderNo, orderNo)
                .orderByDesc(OrdOrder::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdOrder order = ordOrderMapper.selectOne(lqw);
        return BeanUtils.copyProperties(order, RemoteOrderVo.class);
    }

    /**
     * 支付确认
     *
     * @param bo
     * @return
     */
    @Override
    public boolean paymentConfirm(RemoteOrderPaymentBo bo) {
        OrdOrder order = ordOrderMapper.selectById(bo.getOrderId());
        Assert.notNull(order, "订单不存在");
        validBeforeHandle(order.getStatus());
        // 发送websocket消息，通知司机订单操作
        if (ObjectUtils.isNotNull(order.getDriverId()) && order.getDriverId() > 0) {
            ordOrderProcessHelper.asyncSendWebSocketMessage(order.getId(), order.getDriverId());
        }
        // 订单Id
        Long orderId = order.getId();
        if (Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())) {
            log.error("订单{}已支付", orderId);
            return true;
        }

        // 开始付款
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, orderId)
                .set(OrdOrder::getPayStatus, bo.getPayStatusEnum())
                .set(OrdOrder::getPayMode, bo.getPayTypeEnum())
                .set(OrdOrder::getPayTime, bo.getPayTime())
                .set(OrdOrder::getUpdateTime, new Date());
        if (StringUtils.isNotBlank(bo.getThirdStatus())) {
            updateWrapper.set(OrdOrder::getThirdStatus, bo.getThirdStatus());
        }

        boolean flag = ordOrderMapper.update(updateWrapper) > 0;
        if (flag) {
            ordOrderProcessHelper.afterPayment(order.getTenantId(), orderId, order.getEarliestTime(), order.getDriverId());
            // 订单状态变更事件
            ordOrderProcessHelper.asyncSendOrderStatusChangeEvent(orderId);
        }
        return flag;
    }

    /**
     * 根据乘客id查询订单
     *
     * @param passengerIds 乘客id
     * @return 订单信息
     */
    @Override
    public List<RemoteOrderVo> getOrderByPassengerIds(List<Long> passengerIds) {
        if (CollUtils.isEmpty(passengerIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<OrdOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(OrdOrder::getPassengerId, passengerIds);
        return BeanUtils.copyToList(ordOrderMapper.selectList(lambdaQuery), RemoteOrderVo.class);
    }

    @Override
    public List<RemoteOrderVo> getOrderByDriverId(Long driverId) {
        List<OrdOrder> orders = ordOrderMapper.selectList(
                Wrappers.lambdaQuery(OrdOrder.class)
                        .eq(OrdOrder::getDriverId, driverId)
                        .in(OrdOrder::getStatus, OrderStatusEnum.PICK.getCode(), OrderStatusEnum.PICK_START.getCode(), OrderStatusEnum.ING.getCode())
        );
        //获取订单id
        //获取订单id
        List<RemoteOrderVo> remoteOrderVos = BeanUtils.copyToList(orders, RemoteOrderVo.class);
        return remoteOrderVos;
    }

    /**
     * 根据结算时间查询邀请订单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 订单集合
     */
    @Override
    public List<RemoteOrderVo> queryByRebateStatusAndTimeWithComplaint(String rebateStatus, String startTime, String endTime) {
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery(OrdOrder.class)
                .eq(OrdOrder::getRebateStatus, rebateStatus)
                .ge(OrdOrder::getRebateTime, startTime)
                .le(OrdOrder::getRebateTime, endTime)
                .eq(OrdOrder::getComplain, IsYesEnum.YES.getCode());

        List<OrdOrder> ordOrders = ordOrderMapper.selectList(lqw);
        return BeanUtils.copyToList(ordOrders, RemoteOrderVo.class);
    }

    /**
     * 根据结算状态和结算时间查询订单
     *
     * @param rebateStatus 结算状态
     * @param startTime    结算开始时间
     * @param endTime      结算结束时间
     * @return 已结算订单集合
     */
    @Override
    public List<RemoteOrderVo> queryByRebateStatusAndTime(List<String> rebateStatus, String startTime, String endTime) {
        if (CollUtils.isEmpty(rebateStatus)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery(OrdOrder.class)
                .in(OrdOrder::getRebateStatus, rebateStatus)
                .ge(OrdOrder::getRebateTime, startTime)
                .le(OrdOrder::getRebateTime, endTime);

        List<OrdOrder> ordOrders = ordOrderMapper.selectList(lqw);
        return BeanUtils.copyToList(ordOrders, RemoteOrderVo.class);
    }

    /**
     * 根据订单完成时间和平台编码查询订单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    @Override
    public List<RemoteOrderVo> queryByFinishTimeAndPlatformCodes(String startTime, String endTime, List<String> platformCodes) {
        if (CollUtils.isEmpty(platformCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery(OrdOrder.class)
                .eq(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .in(OrdOrder::getPlatformCode, platformCodes)
                .ge(OrdOrder::getFinishTime, startTime)
                .le(OrdOrder::getFinishTime, endTime);

        List<OrdOrder> ordOrders = ordOrderMapper.selectList(lqw);
        return BeanUtils.copyToList(ordOrders, RemoteOrderVo.class);
    }


    /**
     * 操作前校验订单
     */
    private void validBeforeHandle(String orderStatus) {
        String msg = StrUtil.format("状态错误，无法操作", OrderStatusEnum.getInfoByCode(orderStatus));
        Assert.isTrue(!OrderStatusEnum.getFinishedStatus().contains(orderStatus), msg);
    }

    @Override
    public List<RemoteOrderVo> queryOrderByOrderNos(List<String> orderNos) {
        if (CollUtils.isEmpty(orderNos)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery(OrdOrder.class)
                .in(OrdOrder::getOrderNo, orderNos);
        return BeanUtils.copyToList(ordOrderMapper.selectList(lqw), RemoteOrderVo.class);
    }

    @Override
    public Map<Long, Long> queryOrderCountByDriverIdsAndStatuses(List<Long> driverIds, List<String> orderStatuses) {
        if (CollUtils.isEmpty(driverIds) || CollUtils.isEmpty(orderStatuses)) {
            return Collections.emptyMap();
        }
        List<QueryCountBo> queryCountBos = ordOrderMapper.queryOrderCountByDriverIdsAndStatuses(driverIds, orderStatuses);
        if (CollUtils.isEmpty(queryCountBos)) {
            return Collections.emptyMap();
        }
        return queryCountBos.stream().collect(Collectors.toMap(QueryCountBo::getGroupId, QueryCountBo::getNum));
    }

    @Override
    public RemoteOrderVo queryFirstOrderByPassengerId(Long passengerId) {
        if (passengerId == null) {
            return null;
        }
        LambdaQueryWrapper<OrdOrder> lqw = Wrappers.lambdaQuery(OrdOrder.class)
                .eq(OrdOrder::getPassengerId, passengerId)
                .orderByAsc(OrdOrder::getCreateTime)
                .orderByAsc(OrdOrder::getId)
                .last("limit 1");
        OrdOrder ordOrder = ordOrderMapper.selectOne(lqw);
        if (ordOrder == null) {
            return null;
        }
        return BeanUtils.copyProperties(ordOrder, RemoteOrderVo.class);
    }
}
