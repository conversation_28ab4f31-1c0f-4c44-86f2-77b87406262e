package com.feidi.xx.cross.order.chain.handler.common;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.SuccessFailEnum;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.annotations.HandlerScope;
import com.feidi.xx.cross.common.cache.order.enums.OrdCacheKeyEnum;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.market.InviteTypeEnum;
import com.feidi.xx.cross.common.enums.market.RewardTypeEnum;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.RateTypeEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.common.mq.producer.OrdOrderStatusChangeProducer;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.market.api.RemoteCouponGrantService;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.market.api.domain.RemoteInviteRecordVo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainContext;
import com.feidi.xx.cross.order.chain.base.OrderBaseChainResult;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.common.AbstractChainHandler;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainContext;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.helper.OrdOrderHelper;
import com.feidi.xx.cross.order.helper.OrdOrderProcessHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.mq.event.OrderUnbindVirtualPhoneEvent;
import com.feidi.xx.cross.order.mq.producer.OrderUnbindVirtualPhoneProducer;
import com.feidi.xx.cross.order.service.IOrdOrderInsureService;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import com.feidi.xx.cross.order.service.IOrdPositionService;
import com.feidi.xx.cross.order.service.IOrdTrackService;
import com.feidi.xx.push.common.enums.PushTypeEnum;
import com.feidi.xx.resource.api.RemoteVirtualPhoneService;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Date;

/**
 * 订单后置处理器（异步处理）
 * 处理一些非关键操作，可以异步处理的
 * 需要注意异步导致的租户ID丢失！！
 */
@Slf4j
@Component
@HandlerScope
@RequiredArgsConstructor
public class OrderAfterAsyncHandler<T extends OrderBaseChainContext, R extends OrderBaseChainResult> extends AbstractChainHandler<T, R> {

    private final OrdOrderMapper baseMapper;

    private final OrdOrderHelper ordOrderHelper;

    private final OrdOrderInfoMapper ordOrderInfoMapper;

    private final OrdCacheManager ordCacheManager;

    private final IOrdTrackService ordTrackService;

    private final IOrdPositionService orderPositionService;

    private final IOrdOrderInsureService ordOrderInsureService;

    private final IOrdOrderProcessService ordOrderProcessService;

    private final OrdOrderProcessHelper ordOrderProcessHelper;

    private final OrdOrderOperateProducer ordOrderOperateProducer;

    private final OrderUnbindVirtualPhoneProducer ordersUnbindVirtualPhoneProducer;

    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;

    @DubboReference
    private final RemoteVirtualPhoneService virtualPhoneService;


    @DubboReference
    private final RemoteCouponGrantService remoteCouponGrantService;
    @DubboReference
    private final RemoteConfigService configService;

    private final OrdOrderStatusChangeProducer ordOrderStatusChangeProducer;


    /**
     * 需要注意异步导致的租户ID丢失！！不允许使用LoginHelper和StpUtil等需要登录才能获取信息的
     * 需要注意异步导致的租户ID丢失！！不允许使用LoginHelper和StpUtil等需要登录才能获取信息的
     * 需要注意异步导致的租户ID丢失！！不允许使用LoginHelper和StpUtil等需要登录才能获取信息的
     *
     * @param context 责任链执行入参
     */
    @Async
    @Override
    public void handle(T context) {
        TenantHelper.setDynamic(context.getTenantId());
        // 重新查一下，避免前面更新了字段
        OrdOrder order = baseMapper.selectById(context.getOrderId());
        Assert.notNull(order, StrUtil.format("后置处理器，订单{}不存在", context.getOrderId()));
        ExceptionUtil.ignoreEx(() -> {
            if (context instanceof OrderPlaceChainContext placeContext) {
                // 订单判重key
                String dupKey = OrdCacheKeyEnum.ORD_DUPLICATE_KEY.create(placeContext.getOrderKey());
                RedisUtils.setCacheObject(dupKey, dupKey, OrdCacheKeyEnum.ORD_DUPLICATE_KEY.getDuration());
                // 订单判重映射key，后续取消使用，避免重复下单
                String dupKeyMapKey = OrdCacheKeyEnum.ORD_DUPLICATE_KEY.create(placeContext.getOrderId());
                RedisUtils.setCacheObject(dupKeyMapKey, placeContext.getOrderKey(), OrdCacheKeyEnum.ORD_DUPLICATE_KEY.getDuration().plus(OrdCacheKeyEnum.ORD_DUPLICATE_KEY.getDuration()));

            } else if (context instanceof OrderOperateChainContext operateContext) {

                if (OrderStatusEnum.ING.getCode().equals(operateContext.getStatus())) {
                    // 推送消息
                    PushTypeEnum pushType = PushTypeEnum.ORDER_START_TRIP;
                    ordOrderProcessHelper.pushMsgToDriver(operateContext.getTenantId(), pushType, order.getDriverId(), pushType.formatTitle(), pushType.formatContent(), null);

                    //乘客上车30分钟后解绑虚拟号
                    RemoteOrderInfoVo info = ordCacheManager.getOrderSubInfoByOrderId(order.getId());
                    RemoteOrderDriverVo driverVo = ordCacheManager.getOrderDriverInfoByOrderId(order.getId());
                    String unbindTime = configService.selectValueByKey("cross.order.unbind.axb");
                    if (StringUtils.isEmpty(unbindTime)){
                        unbindTime = "30";
                    }
                    DateTime dateTime = DateUtil.offsetMinute(new Date(), Integer.parseInt(unbindTime));
                    log.info("解绑axb消息发送时间为：{}, 延时时间为：{}", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), DateUtil.format(dateTime, "yyyy-MM-dd HH:mm:ss"));
                    ordersUnbindVirtualPhoneProducer.sendMessage(new OrderUnbindVirtualPhoneEvent(order.getId(), dateTime.getTime(), driverVo.getDriverPhone(), info.getVirtualPhone(), order.getPassengerPhone()));
                } else if (OrderStatusEnum.FINISH.getCode().equals(operateContext.getStatus())) {
                    // 订单状态变更事件
                    OrdOrderStatusChangeEvent event = getStatusChangeEvent(order);
                    ordOrderStatusChangeProducer.sendMessage(event);

                    // 邀请有奖返利
                    if (ordOrderHelper.isInviteRewardBoolean()) {
                        RemoteOrderRateVo remoteOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_AGENT.getCode());
                        if (ObjectUtils.isNotNull(remoteOrderRateVo)) {
                            String inviteType;
                            if (ObjectUtils.isNotNull(order.getInviteDriverId())) {
                                inviteType = InviteTypeEnum.DRIVER_INVITE.getCode();
                            } else {
                                inviteType = InviteTypeEnum.AGENT_INVITE.getCode();
                            }
                            RemoteInviteRecordVo build = RemoteInviteRecordVo.builder()
                                    .driverId(order.getDriverId())
                                    .tenantId(order.getTenantId())
                                    .passengerId(order.getPassengerId())
                                    .agentId(remoteOrderRateVo.getUserId())
                                    .orderId(remoteOrderRateVo.getOrderId())
                                    .inviteType(inviteType)
                                    .rewardType(RewardTypeEnum.NEW_USER_REWARD.getCode())
                                    .rewardRate(remoteOrderRateVo.getRate())
                                    .rewardPrice(remoteOrderRateVo.getAmount())
                                    .build();
                            remoteInviteRecordService.insertByBo(build);
                        }
                    }
                    //记录订单轨迹
                    ordTrackService.saveOrderTrackLocation(order.getId(),order.getTenantId());
                }
            } else if (context instanceof OrderCancelChainContext cancelContext) {
                RemoteOrderCancelBo cancelBo = cancelContext.getHandleBo();
                OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(cancelBo.getOrderId(), OperateTypeEnum.CANCEL.getCode(), JsonUtils.toJsonString(cancelBo), cancelBo.getUserType(), cancelBo.getUserId());
                if (cancelContext.isSuccess()) {
                    ordOrderProcessHelper.asyncAfterCancelOrder(order);
                } else {
                    operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
                }

                //解绑虚拟号
                RemoteOrderInfoVo info = ordCacheManager.getOrderSubInfoByOrderId(order.getId());
                RemoteOrderDriverVo driverVo = ordCacheManager.getOrderDriverInfoByOrderId(order.getId());
                //解绑原始绑定信息
                if (driverVo != null && info.getVirtualPhone() != null) {
                    log.info("开始进行虚拟号-解绑操作, 解绑司机手机号为{}, 虚拟号为{}, 乘客手机号为{}",driverVo.getDriverPhone(), info.getVirtualPhone(), order.getPassengerPhone());
                    virtualPhoneService.unbindAxb(driverVo.getDriverPhone(), info.getVirtualPhone() , order.getPassengerPhone(), String.valueOf(order.getId()));
                }
                // 清空订单虚拟号
                ordOrderInfoMapper.update(null, Wrappers.<OrdOrderInfo>lambdaUpdate()
                        .eq(OrdOrderInfo::getOrderId, order.getId())
                        .set(OrdOrderInfo::getVirtualPhone, null));
                ordOrderOperateProducer.sendMessage(operateEvent);
            }
        }, "后置处理器异常", TenantHelper::clearDynamic);
    }

    private static OrdOrderStatusChangeEvent getStatusChangeEvent(OrdOrder order) {
        OrdOrderStatusChangeEvent event = new OrdOrderStatusChangeEvent();
        event.setOrderId(order.getId());
        event.setPassengerId(order.getPassengerId());
        event.setPassengerPhone(order.getPassengerPhone());
        event.setOrderNo(order.getOrderNo());
        event.setTenantId(order.getTenantId());
        event.setAgentId(order.getAgentId());
        event.setDriverId(order.getDriverId());
        event.setProductCode(order.getProductCode());
        event.setOrderPrice(order.getOrderPrice());
        event.setPayPrice(order.getPayPrice());
        event.setStatus(order.getStatus());
        event.setThirdStatus(order.getThirdStatus());
        event.setPayStatus(order.getPayStatus());
        event.setPayMode(order.getPayMode());
        event.setPayTime(order.getPayTime());
        event.setPayNo(order.getPayNo());
        event.setFinishTime(order.getFinishTime());
        return event;
    }
}


