package com.feidi.xx.cross.order.domain.handle.bo;

import lombok.Data;

import java.io.Serializable;

/**
 * 订单支付参数
 */
@Data
public class OrdOrderPaymentBo  implements Serializable {
    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * appid
     */
    private String appId;

    /**
     * 微信openId
     */
    private String openId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 支付方式：1.微信；2.支付宝（只有代客下单支付、才支持支付宝扫码付款）
     * {@link com.feidi.xx.common.core.enums.PaymentTypeEnum}
     */
    private String paymentType;

    /**
     * 小程序码
     */
    private String xcxCode;

    /**
     * 是否代客下单支付:Y.是（生成二维码）；N.直接JS支付
     */
    private String isReplace;

}
