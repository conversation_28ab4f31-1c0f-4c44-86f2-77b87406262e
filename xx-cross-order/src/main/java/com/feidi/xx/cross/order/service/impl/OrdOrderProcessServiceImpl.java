package com.feidi.xx.cross.order.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.platform.enums.PlatformCacheKeyEnum;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.order.OrderConstants;
import com.feidi.xx.cross.common.constant.order.OrderLockKeyConstants;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.components.PlatformOrderApiComponent;
import com.feidi.xx.cross.order.domain.OrdDriver;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderProfitBo;
import com.feidi.xx.cross.order.domain.bo.OrdRateBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.update.DispatchDTO;
import com.feidi.xx.cross.order.domain.vo.OrdDriverVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderProfitVo;
import com.feidi.xx.cross.order.helper.OrdOrderComplainHelper;
import com.feidi.xx.cross.order.helper.OrdOrderHelper;
import com.feidi.xx.cross.order.helper.OrdOrderProcessHelper;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.service.IOrdDriverService;
import com.feidi.xx.cross.order.service.IOrdOrderProcessService;
import com.feidi.xx.cross.order.service.IOrdRateService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.resource.api.RemoteVirtualPhoneService;
import com.feidi.xx.system.api.RemoteConfigService;
import io.seata.spring.annotation.GlobalTransactional;
import io.seata.tm.api.GlobalTransaction;
import io.seata.tm.api.GlobalTransactionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 订单流程Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/3/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderProcessServiceImpl implements IOrdOrderProcessService {

    private final PowCacheManager powCacheManager;
    private final OrdCacheManager ordCacheManager;
    private final OrdOrderHelper ordOrderHelper;
    private final OrdOrderProcessHelper ordOrderProcessHelper;
    private final OrdOrderComplainHelper ordOrderComplainHelper;
    private final IOrdDriverService ordDriverService;
    private final IOrdRateService ordRateService;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    private final PlatformOrderApiComponent platformOrderApiComponent;
    private final MktCacheManager mktCacheManager;
    private final ScheduledExecutorService scheduledExecutorService;
    @DubboReference
    private final RemotePassengerService remotePassengerService;
    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;
    @DubboReference
    private final RemoteVirtualPhoneService virtualPhoneService;
    @DubboReference
    private final RemoteConfigService configService;

    /**
     * 订单调度
     * 司机抢单，自动抢单（司机、代理），派单、改派（合作商、总后台）
     *
     * @param dispatchBo 调度参数
     * @return 是否调度成功
     */
    @Override
    public Boolean dispatchOrder(OrdOrderDispatchBo dispatchBo) {
        boolean result = false;
        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(OrderLockKeyConstants.LOCK_ORDER_DISPATCH_KEY + dispatchBo.getOrderId());
        try {
            boolean isLock = lock.tryLock(OrderLockKeyConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (isLock) {
                OrdOrderProcessServiceImpl ordOrderProcessService = SpringUtils.getBean(OrdOrderProcessServiceImpl.class);
                result = ordOrderProcessService.doDispatch(dispatchBo);
            }
            return result;
        } catch (ServiceException se){
            log.error(se.getMessage(), se);
            log.error("订单接单失败，订单号：{}，原因：{}", dispatchBo.getOrderId(), se.getMessage());
            throw new ServiceException("该订单暂时无法调度：" + se.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("订单接单失败，订单号：{}，原因：{}", dispatchBo.getOrderId(), e.getMessage(), e);
            throw new ServiceException("订单调度异常");
        } finally {
            lock.unlock();
        }
    }

    /**
     * 订单调度
     * 将本地事务与分布式锁分开，保证锁释放之前本地事务已经提交
     *
     * @param dispatchBo 调度参数
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean doDispatch(OrdOrderDispatchBo dispatchBo) {
        String operateType = DispatchTypeEnum.CHANGE.getCode().equals(dispatchBo.getType()) ? OperateTypeEnum.CHANGE.getCode() : OperateTypeEnum.RECEIVE.getCode();
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(dispatchBo.getOrderId(), operateType, JsonUtils.toJsonString(dispatchBo),
                dispatchBo.getUserType(), dispatchBo.getUserId());

        boolean result = true;
        boolean notifyMt = false;
        String remark = DispatchTypeEnum.getInfoByCode(dispatchBo.getType());
        try {
            OrdOrder order = ordOrderMapper.selectById(dispatchBo.getOrderId());
            validBeforeDispatch(order, dispatchBo);

            notifyMt = (!DispatchTypeEnum.CHANGE.getCode().equals(dispatchBo.getType())) && PlatformEnum.MT.getCode().equals(order.getPlatformCode());

            // 调度信息
            OrdOrderDispatchBo.DispatchInfo dispatchInfo = new OrdOrderDispatchBo.DispatchInfo();

            // 更新对象
            DispatchDTO dto = new DispatchDTO(order.getId(), order.getVersion());

            // 绑定司机
            OrdDriver ordDriver = ordDriverService.bindDriver(order, dispatchBo);


            if (Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())) {
                remark = "订单改派给司机【" + ordDriver.getDriverName() + "】";
                // 改派前的司机ID，只有改派才有，不然可能为空
                dispatchInfo.setOriginalDriverId(order.getDriverId());
                // 调度司机ID
                dispatchInfo.setDispatchDriverId(ordDriver.getDriverId());
                dispatchBo.setDispatchInfo(dispatchInfo);
            }

            dto.setDriverId(ordDriver.getDriverId());
            dto.setAgentId(ordDriver.getAgentId());
            dto.setDispatchType(dispatchBo.getType());
            if (Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())) {
                dto.setShowed(IsYesEnum.YES.getCode());
            }
            // 更新订单字段，后续需要用到
            dto.update(order);

            // 更新原有订单收益状态
            if (Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())) {
                ordOrderComplainHelper.updateOrderProfitAfterComplain(order.getId(), StatusEnum.DISABLE.getCode());
            }

            // 获取收益
            OrdOrderProfitBo cxProfitBo = ordOrderHelper.createOrderProfitBo(order);
            cxProfitBo.setForceUpdate(true);
            OrdOrderProfitVo profitVo = ordOrderHelper.calculateOrderProfit(cxProfitBo);
            this.batchAddOrderRate(order, profitVo);

            // 更新数据库和order对象
            result &= ordOrderMapper.update(dto);
            RemoteOrderInfoVo info = ordCacheManager.getOrderSubInfoByOrderId(order.getId());
            OrdDriverVo driverVo = ordDriverService.queryLatestByOrderIdAndDispatchDriverId(order.getId(), dispatchInfo.getOriginalDriverId());
            //改派解绑原始绑定信息
            if (driverVo != null && info.getVirtualPhone() != null) {
                log.info("开始进行虚拟号-解绑操作, 解绑司机手机号为{}, 虚拟号为{}, 乘客手机号为{}",driverVo.getDriverPhone(), info.getVirtualPhone(), order.getPassengerPhone());
                virtualPhoneService.unbindAxb(driverVo.getDriverPhone(), info.getVirtualPhone() ,order.getPassengerPhone(), String.valueOf(order.getId()));
            }
            // 改派清空订单虚拟号
            ordOrderInfoMapper.update(null, Wrappers.<OrdOrderInfo>lambdaUpdate()
                            .eq(OrdOrderInfo::getOrderId, order.getId())
                            .set(OrdOrderInfo::getVirtualPhone, null));

            /*String s = configService.selectValueByKey(OrderConstants.CROSS_ORDER_PROCESS_AXB);
            if (!StringUtils.isEmpty(s) && IsYesEnum.YES.getCode().equals(s)){
                // 虚拟号
                if (PlatformEnum.SELF.getCode().equals(order.getPlatformCode())) {
                    String virtualPhone = null;
                    try {
                        log.info("开始进行虚拟号操作");
                        RemoteOrderInfoVo info = ordCacheManager.getOrderSubInfoByOrderId(order.getId());
                        if (DispatchTypeEnum.CHANGE.getCode().equals(dispatchBo.getType())) {
                            // 解绑旧关系
                            log.info("开始进行虚拟号-换绑操作");
                            OrdDriverVo driverVo = ordDriverService.queryLatestByOrderIdAndDispatchDriverId(order.getId(), dispatchInfo.getOriginalDriverId());
                            if (driverVo != null) {
                                log.info("开始进行虚拟号-解绑操作, 解绑司机手机号为{}, 虚拟号为{}, 乘客手机号为{}",driverVo.getDriverPhone(), info.getVirtualPhone(), order.getPassengerPhone());
                                virtualPhoneService.unbindAxb(driverVo.getDriverPhone(), info.getVirtualPhone() ,order.getPassengerPhone(), String.valueOf(order.getId()));
                            }
                        }
                        log.info("开始进行虚拟号-绑定操作, 绑定新司机手机号为{}, 乘客手机号为{}",ordDriver.getDriverPhone(), order.getPassengerPhone());
                        virtualPhone = virtualPhoneService.bindAxb(ordDriver.getDriverPhone(),order.getPassengerPhone(), DateUtil.offsetHour(order.getLatestTime(), 12), String.valueOf(order.getId()));
                        log.info("开始进行虚拟号-绑定操作, 新虚拟号为{}", virtualPhone);
                    } catch (Exception e) {
                        log.error("获取虚拟号失败" + e.getMessage());
                    } finally {
                        // 对于获取调度号的结果，如果获取不到需要把原本的虚拟号移除
                        // 绑定虚拟号错误不影响最终调度结果，所以不进行result &= ...操作
                        ordOrderInfoMapper.update(null, Wrappers.<OrdOrderInfo>lambdaUpdate()
                                .eq(OrdOrderInfo::getOrderId, order.getId())
                                .set(OrdOrderInfo::getVirtualPhone, virtualPhone)
                        );
                    }
                }
            }*/

            // 异步操作
            ordOrderProcessHelper.afterDispatch(result, order, dispatchBo, operateEvent, dispatchInfo);
            // 不需要通知美团的可以直接保存日志，通知美团的由美团完成日志的剩余部分再保存
            operateEvent.setResponseJson(JsonUtils.toJsonString(result));
            operateEvent.setRemark(remark + SuccessFailEnum.SUCCESS.getInfo());
            return result;
        } catch (ServiceException se) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setRemark("订单调度失败：" +  se.getMessage());
            throw se;
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setRemark(remark + SuccessFailEnum.FAIL.getInfo());
            throw e;
        } finally {
            if (!notifyMt) {
                ordOrderOperateProducer.sendMessage(operateEvent);
            }
        }
    }

    /**
     * 订单操作
     *
     * @param handleBo 订单操作参数
     * @return 是否操作成功
     */
    @Override
    public Boolean handleOrder(OrdOrderHandleBo handleBo) {
        // 操作结果集
        boolean flag;
        String errorMsg = "";
        RemotePlatformApiResponseVo platformApiResponseVo = null;
        String operateType = Objects.equals(handleBo.getStatus(), OrderStatusEnum.PICK.getCode()) ? OperateTypeEnum.PICK.getCode() : OperateTypeEnum.PICK_START.getCode();
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(handleBo.getOrderId(), operateType, JsonUtils.toJsonString(handleBo));

        try {
            // 订单信息
            OrdOrder order = ordOrderMapper.selectById(handleBo.getOrderId());
            validBeforeHandle(order.getStatus());

            // 自营平台订单需要校验是否支付
            if (!Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
                // 校验订单支付状态
                if (!Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())) {
                    errorMsg = "订单未支付";
                    throw new ServiceException(errorMsg);
                }
            }

            if (!(Integer.parseInt(handleBo.getStatus()) > Integer.parseInt(order.getStatus()))) {
                errorMsg = "订单状态无法操作，请刷新页面重试";
                throw new ServiceException(errorMsg);
            }

            /// 修改订单状态
            LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
            // 使用乐观锁，防止订单状态被覆盖
            updateWrapper.eq(OrdOrder::getId, order.getId())
                    .eq(OrdOrder::getVersion, order.getVersion())
                    .set(OrdOrder::getVersion, order.getVersion() + 1)
                    .set(StringUtils.isNotBlank(handleBo.getThirdStatus()), OrdOrder::getThirdStatus, handleBo.getThirdStatus());

            // 订单操作节点
            if (Objects.equals(handleBo.getStatus(), OrderStatusEnum.PICK.getCode())) {
                // 出发去接乘客
                updateWrapper.set(OrdOrder::getStatus, OrderStatusEnum.PICK.getCode());
                updateWrapper.set(OrdOrder::getPickTime, DateUtils.getNowDate());
            } else if (Objects.equals(handleBo.getStatus(), OrderStatusEnum.PICK_START.getCode())) {
                // 到达乘客起点
                updateWrapper.set(OrdOrder::getStatus, OrderStatusEnum.PICK_START.getCode());
                updateWrapper.set(OrdOrder::getArrivalTime, DateUtils.getNowDate());
            }

            // 通知第三方
            String success = SuccessFailEnum.SUCCESS.getCode();
            if (!order.getPlatformCode().equals(PlatformCodeEnum.SELF.getCode()) && StpUtil.isLogin()) {
                platformApiResponseVo = ordOrderProcessHelper.thirdPlatformApi(order, operateType);
                if (!Objects.equals(platformApiResponseVo.getStatus(), SuccessFailEnum.SUCCESS.getCode())) {
                    success = SuccessFailEnum.FAIL.getCode();
                }
            }

            // 处理结果集
            if (SuccessFailEnum.SUCCESS.getCode().equals(success)) {
                flag = ordOrderMapper.update(updateWrapper) > 0;
                // 推送消息
                ordOrderProcessHelper.afterHandle(order.getTenantId(), order.getId(), handleBo.getStatus(), order.getDriverId(), order.getPassengerId());
            } else {
                throw new ServiceException("操作失败 " + platformApiResponseVo.getMsg());
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("订单操作失败，订单号：{}，原因：{}", handleBo.getOrderId(), e.getMessage(), e);
            throw new ServiceException(errorMsg);
        } finally {
            operateEvent.setResponseJson(JsonUtils.toJsonString(platformApiResponseVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return flag;
    }

    /**
     * 行程开始
     *
     * @param handleBo 行程开始参数
     * @return 行程开始结果
     */
    @Override
    public Boolean tripStart(OrdOrderHandleBo handleBo) {
        boolean flag;
        RemotePlatformApiResponseVo platformApiResponseVo = null;
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(handleBo.getOrderId(), OperateTypeEnum.INTO.getCode(), JsonUtils.toJsonString(handleBo));
        try {
            // 订单信息
            OrdOrder order = ordOrderMapper.selectById(handleBo.getOrderId());
            // 校验订单状态
            validBeforeHandle(order.getStatus());

            if (!Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())) {
                throw new ServiceException("订单未支付、请联系乘客或客服");
            }

            if (!(Integer.parseInt(OrderStatusEnum.ING.getCode()) > Integer.parseInt(order.getStatus()))) {
                throw new ServiceException("订单状态无法操作，请刷新页面重试");
            }

            // 订单操作日志
            // 现阶段只有美团在行程开始时，需要通知第三方
            String success = SuccessFailEnum.SUCCESS.getCode();
            if (!order.getPlatformCode().equals(PlatformCodeEnum.SELF.getCode()) && StpUtil.isLogin()) {
                // 通知第三方
                platformApiResponseVo = ordOrderProcessHelper.thirdPlatformApi(order, OperateTypeEnum.INTO.getCode());
                if (!Objects.equals(platformApiResponseVo.getStatus(), SuccessFailEnum.SUCCESS.getCode())) {
                    success = SuccessFailEnum.FAIL.getCode();
                }
            }

            // 处理远程调用结果
            if (!SuccessFailEnum.SUCCESS.getCode().equals(success)) {
                throw new ServiceException("行程开始操作失败： " + platformApiResponseVo.getMsg());
            }

            flag = ordOrderProcessHelper.afterTripStart(order, handleBo.getThirdStatus());
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("行程开始失败，订单号：{}，原因：{}", handleBo.getOrderId(), e.getMessage());
            throw new ServiceException("行程开始失败");
        } finally {
            operateEvent.setResponseJson(JsonUtils.toJsonString(platformApiResponseVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return flag;
    }

    /**
     * 行程结束
     *
     * @param handleBo 行程结束参数
     * @return 行程结束结果
     */
    @Override
    public Boolean tripEnd(OrdOrderHandleBo handleBo) {
        boolean flag = false;
        RemotePlatformApiResponseVo platformApiResponseVo = null;
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(handleBo.getOrderId(), OperateTypeEnum.FINISH.getCode(), JsonUtils.toJsonString(handleBo));

        // 订单信息
        OrdOrder order = ordOrderMapper.selectById(handleBo.getOrderId());
        // 校验订单信息
        validBeforeHandle(order.getStatus());

        RedissonClient client = RedisUtils.getClient();
        RLock lock = client.getLock(OrderLockKeyConstants.LOCK_ORDER_FINISH_KEY + order.getDriverId());
        try {
            boolean tryLock = lock.tryLock(OrderLockKeyConstants.LOCK_EXPIRE_TIME, TimeUnit.SECONDS);
            if (tryLock) {
                // 手动管理事务
                GlobalTransaction tx = GlobalTransactionContext.getCurrentOrCreate();
                // 开始事务
                tx.begin();

                try {
                    // 双重校验
                    order = ordOrderMapper.selectById(handleBo.getOrderId());
                    validBeforeHandle(order.getStatus());

                    if (!Objects.equals(order.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())) {
                        throw new ServiceException("订单未支付、请联系乘客或客服");
                    }

                    if (!(Integer.parseInt(OrderStatusEnum.FINISH.getCode()) > Integer.parseInt(order.getStatus()))) {
                        throw new ServiceException("订单状态无法操作，请刷新页面重试");
                    }

                    // 通知第三方
                    String success = SuccessFailEnum.SUCCESS.getCode();
                    if (!order.getPlatformCode().equals(PlatformCodeEnum.SELF.getCode()) && StpUtil.isLogin()) {
                        platformApiResponseVo = ordOrderProcessHelper.thirdPlatformApi(order, OperateTypeEnum.FINISH.getCode());
                        if (!Objects.equals(platformApiResponseVo.getStatus(), SuccessFailEnum.SUCCESS.getCode())) {
                            success = SuccessFailEnum.FAIL.getCode();
                        }
                    }
                    OrdOrder finalOrder = order;
                    if (Objects.equals(order.getCreateModel(), CreateModelEnum.AGENT_ORDER.getCode())) {
                        log.info("代理商代客下单");
                        // 乘客代下单
                        if (mktCacheManager.getPassengerId(UserTypeEnum.AGENT_USER.getUserType(), order.getAgentId(), order.getPassengerId())) {
                            //异步
                            scheduledExecutorService.schedule(() -> {
                                remotePassengerService.bindPassenger(UserTypeEnum.AGENT_USER.getUserType(), finalOrder.getAgentId(),null, finalOrder.getPassengerId());
                            }, 0, TimeUnit.SECONDS);
                        }
                    }else if (mktCacheManager.getPassengerId(UserTypeEnum.DRIVER_USER.getUserType(), order.getDriverId(), order.getPassengerId())) {
                        log.info("司机代下单");
                        //异步
                        scheduledExecutorService.schedule(() -> {
                            remotePassengerService.bindPassenger(UserTypeEnum.DRIVER_USER.getUserType(), finalOrder.getAgentId(), finalOrder.getDriverId(), finalOrder.getPassengerId());
                        }, 0, TimeUnit.SECONDS);
                    }
                    // 处理结果集
                    if (!SuccessFailEnum.SUCCESS.getCode().equals(success)) {
                        throw new ServiceException("操作失败 " + platformApiResponseVo.getMsg());
                    }

                    flag = ordOrderProcessHelper.afterTripEnd(order, handleBo.getThirdStatus());

                    tx.commit();
                    return flag;
                } catch (Exception e) {
                    tx.rollback();
                    throw new ServiceException("行程结束处理异常：" + e.getMessage());
                }
            } else {
                log.error("订单【{}】无法获取分布式锁，无法操作", handleBo.getOrderId());
                throw new ServiceException("订单【" + handleBo.getOrderId() + "】无法获取分布式锁，无法操作");
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error(e.getMessage(), e);
            throw new ServiceException("行程结束处理异常，" + e.getMessage());
        } finally {
            lock.unlock();
            operateEvent.setResponseJson(JsonUtils.toJsonString(platformApiResponseVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(OrdOrderCancelBo cancelBo) {
        log.info("订单取消参数：cancelBo 【{}】", JsonUtils.toJsonString(cancelBo));
        boolean flag = false;
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(cancelBo.getOrderId(), OperateTypeEnum.CANCEL.getCode(), JsonUtils.toJsonString(cancelBo));
        try {
            String uniKey = StrUtil.format("{}#{}#{}", cancelBo.getOrderId(), cancelBo.getPlatformCode(), cancelBo.getPlatformNo());
            // 手动加锁5秒，如果存在则不处理
            boolean ifAbsent = RedisUtils.setObjectIfAbsent("cancel:" + uniKey, uniKey, Duration.ofSeconds(5));
            if (!ifAbsent) {
                return true;
            }

            // 订单信息
            OrdOrder order = ordOrderMapper.selectById(cancelBo.getOrderId());
            if (ObjectUtils.isNull(order)) {
                return false;
            }

            // 抛异常会导致美团侧无法更新订单状态
            if (Objects.equals(order.getStatus(), OrderStatusEnum.CANCEL.getCode())) {
                log.error("订单已取消，订单id：【{}】", order.getId());
                return true;
            }

            // 只有美团订单，且用户类型也是美团时才不可取消已完成订单
            if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.MT.getCode()) &&
                    Objects.equals(cancelBo.getUserType(), UserTypeEnum.MT.getUserType()) &&
                    Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode())) {
                throw new ServiceException("订单已完成、无法取消，请联系客服处理");
            }

            // 修改订单状态
            LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OrdOrder::getId, order.getId())
                    // 保证同一时刻内 取消订单状态不会被其他操作覆盖
                    .set(OrdOrder::getVersion, order.getVersion() + 1)
                    .set(OrdOrder::getStatus, OrderStatusEnum.CANCEL.getCode())
                    .set(OrdOrder::getCancelUserType, cancelBo.getUserType())
                    .set(OrdOrder::getCancelType, cancelBo.getCancelType())
                    .set(OrdOrder::getCancelTime, DateUtils.getNowDate())
                    // 第三方订单状态
                    .set(StringUtils.isNotBlank(cancelBo.getThirdStatus()), OrdOrder::getThirdStatus, cancelBo.getThirdStatus());

            // 修改订单关联表信息
            LambdaUpdateWrapper<OrdOrderInfo> infoUpdateWrapper = new LambdaUpdateWrapper<>();
            infoUpdateWrapper.eq(OrdOrderInfo::getOrderId, order.getId())
                    .set(OrdOrderInfo::getCancelUserId, cancelBo.getUserId())
                    .set(OrdOrderInfo::getCancelRemark, cancelBo.getCancelRemark())
                    .set(OrdOrderInfo::getUpdateTime, DateUtils.getNowDate());

            // 订单已完单，在进行取消操作 - 视为客诉
            if (Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode())) {
                if (Objects.equals(order.getComplain(), IsYesEnum.YES.getCode())) {
                    throw new ServiceException("订单已客诉完成，请无重复客诉");
                }
                // 客诉
                operateEvent.setOperateType(OperateTypeEnum.COMPLAIN.getCode());
                // 完单后客诉，不再修改订单状态
                updateWrapper.set(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode());

                cancelBo.setComplain(IsYesEnum.YES.getCode());
                cancelBo.setComplainType(ComplainTypeEnum.OTHER.getCode());
                // 客诉时间
                cancelBo.setComplainTime(DateUtils.getNowDate());
                ordOrderComplainHelper.complain(BeanUtils.copyProperties(cancelBo, RemoteOrderCancelBo.class), order);

                // 美团退款
                // 美团订单，且是后台管理员操作
                if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.MT.getCode()) &&
                        Objects.equals(cancelBo.getUserType(), UserTypeEnum.SYS_USER.getUserType())) {
                    RemotePlatformApiResponseVo resp = ordOrderComplainHelper.applyRefund(order);
                    if (!SuccessFailEnum.SUCCESS.getCode().equals(resp.getStatus())) {
                        log.error("美团退款失败：{}", JSONUtil.toJsonStr(resp));
                        throw new ServiceException("退款失败：" + resp.getMsg());
                    }
                }else if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())){
                    // 如果自营的订单看是否有邀请有奖的账单
                    RemoteOrderRateVo remoteOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_AGENT.getCode());
                    if (ObjectUtils.isNotNull(remoteOrderRateVo)){
                        Long userId = remoteOrderRateVo.getUserId();
                        //更新代理商流水类型
                        remoteInviteRecordService.updateRebateStatus(userId,remoteOrderRateVo.getOrderId());
                    }
                }
            } else {
                // 返利状态 - 取消
                updateWrapper.set(OrdOrder::getRebateStatus, RebateStatusEnum.CANCEL.getCode());

                // 自营平台订单取消后，需要把乘客已使用的优惠券返还（客诉不需要返还）
                if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
                    ordOrderProcessHelper.updateCouponGrantStatus(order, CouponStatusEnum.NOT_USED.getCode());
                }
            }

            // 退款 退完钱再取消，退不了钱不取消订单
            boolean refund = ordOrderProcessHelper.refund(order);
            if (refund) {
                log.info("订单退款成功，订单id：【{}】", order.getId());
                // 先完成客诉，在更新订单数据，否则在客诉时校验订单状态会报错
                flag = ordOrderMapper.update(updateWrapper) > 0;
                flag &= ordOrderInfoMapper.update(infoUpdateWrapper) > 0;
                if (flag) {
                    // 异步操作
                    ordOrderProcessHelper.asyncAfterCancelOrder(order);
                }
                operateEvent.setResponseJson(JsonUtils.toJsonString(flag));
            } else {
                throw new ServiceException("订单退款失败，请联系管理员");
            }

            // 发送websocket消息，通知司机订单取消
            if (flag && !Objects.equals(LoginHelper.getUserType(), UserTypeEnum.DRIVER_USER)) {
                if (ObjectUtils.isNotNull(order.getDriverId()) && order.getDriverId() > 0) {
                    ordOrderProcessHelper.asyncSendWebSocketMessage(order.getId(), order.getDriverId());
                }
            }
            //订单状态变化事件
            ordOrderProcessHelper.asyncSendOrderStatusChangeEvent(order.getId());
        } catch (ServiceException se) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setResponseJson(se.getMessage());
            log.error(se.getMessage(), se);
            throw new ServiceException(se.getMessage());
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            operateEvent.setResponseJson(e.getMessage());
            log.error(e.getMessage(), e);
            throw new ServiceException("行程取消处理异常，请联系管理员");
        } finally {
            ordOrderOperateProducer.sendMessage(operateEvent);
        }
        return flag;
    }

    /**
     * 获取虚拟电话
     *
     * @param orderId 订单id
     * @param platformNo 平台编号
     * @return 虚拟电话
     */
    @Override
    public String getVirtualPhone(Long orderId, String platformNo) {
        String virtualPhone = null;
        // 操作记录
        OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildMtOrderOperateEvent(orderId, OperateTypeEnum.VIRTUAL.getCode(), JsonUtils.toJsonString(orderId));
        String remark = "美团 - 获取乘客虚拟号";
        RemotePlatformApiResponseVo platformApiResponseVo = null;
        try {
            // 订单调度司机信息
            //RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(orderId);
            RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDriverInfoByOrderId(orderId);
            // 获取乘客手机号
            platformApiResponseVo = platformOrderApiComponent.queryPassengerPhone(platformNo, orderDriverVo.getDriverPhone());

            if (Objects.equals(platformApiResponseVo.getStatus(), SuccessFailEnum.FAIL.getCode())) {
                String errorMessage = platformApiResponseVo.getMsg();
                operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
                log.error("调用美团获取乘客手机号接口失败，原因：{}", errorMessage);
                remark = remark + " - 失败 - " + errorMessage;
            } else {
                String data = platformApiResponseVo.getData();
                if (StringUtils.isNotBlank(data)) {
                    Dict dict = JsonUtils.parseMap(data);
                    virtualPhone = dict.getStr("mtPhone");
                    if (log.isInfoEnabled()) {
                        log.info("调用美团获取乘客手机号接口成功，美团返回乘客手机号：{}", virtualPhone);
                    }
                    remark = remark + " - 成功 - 虚拟手机号：" + virtualPhone;

                    if (StringUtils.isNotBlank(virtualPhone)) {
                        // 更新虚拟号
                        LambdaUpdateWrapper<OrdOrderInfo> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(OrdOrderInfo::getOrderId, orderId)
                                .set(OrdOrderInfo::getVirtualPhone, virtualPhone);
                        ordOrderInfoMapper.update(updateWrapper);

                        // 美团建立“司机-虚拟号-乘客”的绑定关系，并返回虚拟号，单次绑定时长30分钟
                        String cacheKey = PlatformCacheKeyEnum.PLATFORM_VIRTUAL_PHONE_KEY.create(orderId);
                        RedisUtils.setCacheObject(cacheKey, virtualPhone, PlatformCacheKeyEnum.PLATFORM_VIRTUAL_PHONE_KEY.getDuration());
                    }
                }
            }
        } catch (Exception e) {
            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
            log.error("[消费者] 美团虚拟电话 - 执行消费逻辑，发生异常：{}", e.getMessage());
        } finally {
            operateEvent.setRemark(remark);
            operateEvent.setResponseJson(JsonUtils.toJsonString(platformApiResponseVo));
            ordOrderOperateProducer.sendMessage(operateEvent);
        }

        return virtualPhone;
    }

    /**
     * 调度前校验订单
     */
    private void validBeforeDispatch(OrdOrder order, OrdOrderDispatchBo dispatchBo) {
        if (Objects.isNull(order)) {
            throw new ServiceException("订单不存在");
        }

        // 订单状态为已接单，且调度类型不是改派，则不允许调度
        if (Objects.equals(order.getStatus(), OrderStatusEnum.RECEIVE.getCode())
                && !Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())) {
            throw new ServiceException("该订单已被司机接单，请刷新列表后重试");
        }

        String msg = StrUtil.format("当前订单状态为：{}，无法操作", OrderStatusEnum.getInfoByCode(order.getStatus()));
        if (OrderStatusEnum.getFinishedStatus().contains(order.getStatus())) {
            throw new ServiceException(msg);
        }

        if (!OrderStatusEnum.getDispatchStatus().contains(order.getStatus())) {
            throw new ServiceException("订单无法" + DispatchTypeEnum.getInfoByCode(dispatchBo.getType()));
        }
        if (Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())
                && (dispatchBo.getDriverId().equals(order.getDriverId()))) {
            throw new ServiceException("不能改派同一个司机");
        }

        if (!Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())
                && Objects.equals(order.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode()) && Objects.equals(order.getResellDriverId(), dispatchBo.getDriverId())) {
            throw new ServiceException("转卖司机不能接单");
        }

        if (Objects.equals(order.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
            RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(dispatchBo.getDriverId());
            if (ObjectUtils.isNotNull(driverVo) && !Objects.equals(driverVo.getAgentId(), order.getResellAgentId())) {
                throw new ServiceException("调度司机所属代理商不同");
            }
        }
        // 司机代下单不支持改派
        //if (Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())
        //        && Objects.equals(order.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
        //    throw new ServiceException("该订单是司机代下单，暂不支持改派");
        //}
    }


    /**
     * 批量添加订单费率
     *
     * @param order
     * @param profitVo
     */
    private void batchAddOrderRate(OrdOrder order, OrdOrderProfitVo profitVo) {
        List<OrdRateBo> ordRateBos = new ArrayList<>();
        if (ObjectUtils.isNotNull(profitVo.getPlatformProfit())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getPlatformRate(), profitVo.getPlatformProfit(), RateTypeEnum.PLATFORM.getCode());
            ordRateBo.setTotal(order.getOrderPrice());
            ordRateBos.add(ordRateBo);
        }
        if (ObjectUtils.isNotNull(profitVo.getParentAgentProfit())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getParentAgentRate(), profitVo.getParentAgentProfit(), RateTypeEnum.PARENT_AGENT.getCode());
            RemoteAgentVo agentVo = powCacheManager.getAgentInfoById(order.getAgentId());
            ordRateBo.setUserId(agentVo.getParentId())
                    .setTotal(order.getOrderPrice());
            ordRateBos.add(ordRateBo);
        }
        if (ObjectUtils.isNotNull(profitVo.getAgentProfit())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getAgentRate(), profitVo.getAgentProfit(), RateTypeEnum.AGENT.getCode());
            ordRateBo.setUserId(order.getAgentId())
                    .setUserType(UserTypeEnum.AGENT_USER.getUserType())
                    .setTotal(order.getOrderPrice());

            ordRateBos.add(ordRateBo);
        }
        if (ObjectUtils.isNotNull(profitVo.getDriverProfit())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getDriverRate(), profitVo.getDriverProfit(), RateTypeEnum.DRIVER.getCode());
            ordRateBo.setUserId(order.getDriverId())
                    .setUserType(UserTypeEnum.DRIVER_USER.getUserType())
                    .setTotal(order.getOrderPrice());
            ordRateBos.add(ordRateBo);
        }

        if (ObjectUtils.isNotNull(profitVo.getAgentInviteReward())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getAgentInviteRatio(), profitVo.getAgentInviteReward(), RateTypeEnum.INVITE_AGENT.getCode());
            ordRateBo.setUserId(order.getAgentId())
                    .setUserType(UserTypeEnum.AGENT_USER.getUserType())
                    .setTotal(order.getOrderPrice());
            ordRateBos.add(ordRateBo);
        }
        if (ObjectUtils.isNotNull(profitVo.getDriverInviteReward())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getDriverInviteRatio(), profitVo.getDriverInviteReward(), RateTypeEnum.INVITE_DRIVER.getCode());
            ordRateBo.setUserId(order.getDriverId())
                    .setUserType(UserTypeEnum.DRIVER_USER.getUserType())
                    .setTotal(order.getOrderPrice());
            ordRateBos.add(ordRateBo);
        }
        if (ObjectUtils.isNotNull(profitVo.getResellDriverProfit())) {
            OrdRateBo ordRateBo = createOrdRateBo(order.getId(), profitVo.getResellDriverRate(), profitVo.getResellDriverProfit(), RateTypeEnum.RESELL_DRIVER.getCode());
            ordRateBo.setUserId(order.getResellDriverId())
                    .setUserType(UserTypeEnum.DRIVER_USER.getUserType())
                    .setTotal(order.getOrderPrice());
            ordRateBos.add(ordRateBo);
        }
        if (CollUtils.isNotEmpty(ordRateBos)) {
            ordRateBos.forEach(rateBo -> rateBo.setTenantId(order.getTenantId()));
            // 批量新增
            ordRateService.insertByBos(ordRateBos);
        }
    }

    private OrdRateBo createOrdRateBo(Long orderId, BigDecimal rate, Long amount, String rateType) {
        return new OrdRateBo()
                .setOrderId(orderId)
                .setRateType(rateType)
                .setRate(rate)
                .setAmount(amount)
                .setStatus(StatusEnum.ENABLE.getCode())
                .setSort(Integer.parseInt(rateType));
    }

    /**
     * 操作前校验订单
     */
    private void validBeforeHandle(String orderStatus) {
        String msg = StrUtil.format("当前订单状态为：{}，无法操作", OrderStatusEnum.getInfoByCode(orderStatus));
        Assert.isTrue(!OrderStatusEnum.getFinishedStatus().contains(orderStatus), msg);
    }
}
