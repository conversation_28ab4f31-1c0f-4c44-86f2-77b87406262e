package com.feidi.xx.cross.order.helper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.feidi.xx.common.core.constant.Constants;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.insure.enums.FileTypeEnum;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.mail.utils.MailUtils;
import com.feidi.xx.common.payment.domain.refund.bo.RefundBo;
import com.feidi.xx.common.payment.domain.refund.vo.RefundVo;
import com.feidi.xx.common.payment.mq.PaymentRecordEvent;
import com.feidi.xx.common.payment.mq.PaymentRecordProducer;
import com.feidi.xx.common.payment.strategy.IPaymentService;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.enums.market.InviteTypeEnum;
import com.feidi.xx.cross.common.enums.market.RewardTypeEnum;
import com.feidi.xx.cross.common.enums.message.OrderMessageTypeEnum;
import com.feidi.xx.cross.common.enums.message.WebSocketTypeEnum;
import com.feidi.xx.cross.common.enums.order.*;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.event.OrdOrderStatusChangeEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.common.mq.producer.OrdOrderStatusChangeProducer;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.RemotePaymentRecordService;
import com.feidi.xx.cross.finance.api.domain.bo.RemoteRebateBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemotePaymentRecordVo;
import com.feidi.xx.cross.market.api.RemoteCouponGrantService;
import com.feidi.xx.cross.market.api.RemoteInviteRecordService;
import com.feidi.xx.cross.market.api.domain.RemoteInviteRecordVo;
import com.feidi.xx.cross.market.api.domain.couponGrant.RemoteCouponGrantBo;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.message.api.RemoteWebSocketService;
import com.feidi.xx.cross.message.api.domain.RemoteImMsgInfo;
import com.feidi.xx.cross.message.api.domain.dto.OrderMessage;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import com.feidi.xx.cross.message.api.enums.DiyMessageEnum;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderBo;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderCancelBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDriverVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.api.domain.vo.RemotePositionVo;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainContext;
import com.feidi.xx.cross.order.components.PlatformOrderApiComponent;
import com.feidi.xx.cross.order.domain.OrdOrder;
import com.feidi.xx.cross.order.domain.OrdOrderInfo;
import com.feidi.xx.cross.order.domain.bo.OrdOrderInfoBo;
import com.feidi.xx.cross.order.domain.bo.OrdPositionBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderDispatchBo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.cross.order.mapper.OrdOrderInfoMapper;
import com.feidi.xx.cross.order.mapper.OrdOrderMapper;
import com.feidi.xx.cross.order.mq.event.OrderRobEvent;
import com.feidi.xx.cross.order.mq.producer.OrderRobProducer;
import com.feidi.xx.cross.order.service.*;
import com.feidi.xx.cross.platform.api.hbk.domain.hbk.vo.RemotePlatformApiResponseVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import com.feidi.xx.push.common.constants.VoiceConstant;
import com.feidi.xx.push.common.enums.PushTypeEnum;
import com.feidi.xx.push.mq.PushEvent;
import com.feidi.xx.push.mq.PushMsgProducer;
import com.feidi.xx.resource.api.RemoteVirtualPhoneService;
import com.feidi.xx.system.api.RemoteDictService;
import com.feidi.xx.system.api.domain.vo.RemoteDictDataVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 订单流程帮助类
 * 此类仅用于提取订单相关流程的公共方法，与业务相关操作请勿修改
 * 如有差异化需求，请在自己的策略实现类中进行修改
 *
 * <AUTHOR>
 * @date 2025/3/25
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class OrdOrderProcessHelper {

    private final PowCacheManager powCacheManager;
    @Value("${emergency.email}")
    private String emergencyEmail;

    private final IPaymentService paymentService;
    private final OrdCacheManager ordCacheManager;
    private final OrdOrderHelper ordOrderHelper;
    private final OrdOrderComplainHelper ordOrderComplainHelper;
    private final IOrdTrackService ordTrackService;
    private final IOrdOrderInfoService ordOrderInfoService;
    private final IOrdPositionService orderPositionService;
    private final OrderRobProducer orderRobProducer;
    private final OrdOrderMapper ordOrderMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    private final IOrdOrderInsureService ordOrderInsureService;
    private final IOrdOrderRebateService orderOrderRebateService;
    private final ScheduledExecutorService scheduledExecutorService;
    private final OrdOrderOperateProducer ordOrderOperateProducer;
    private final PlatformOrderApiComponent platformOrderApiComponent;

    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;
    @DubboReference
    private final RemoteInviteRecordService remoteInviteRecordService;
    @DubboReference
    private final RemoteDictService remoteDictService;
    @DubboReference
    private final RemoteImService remoteImService;
    @DubboReference
    private final RemoteWebSocketService remoteWebSocketService;
    @DubboReference
    private final RemotePaymentRecordService remotePaymentRecordService;
    @DubboReference
    private final RemoteVirtualPhoneService virtualPhoneService;
    @DubboReference
    private final RemoteCouponGrantService remoteCouponGrantService;
    private final OrdOrderStatusChangeProducer ordOrderStatusChangeProducer;
    /**
     * 订单创建后处理
     *
     * @param order 订单信息
     * @param remoteOrderBo 订单参数
     */
    public void afterPostOrder(OrdOrder order, RemoteOrderBo remoteOrderBo) {
        // 订单子信息
        List<OrdPositionBo> orderPositionBos = new ArrayList<>();
        OrdOrderInfoBo ordOrderInfoBo = new OrdOrderInfoBo();
        ordOrderInfoBo.setTenantId(order.getTenantId());
        ordOrderInfoBo.setOrderId(order.getId());
        ordOrderInfoBo.setBasePrice(order.getOrderPrice());
        ordOrderInfoBo.setPhoneEnd(remoteOrderBo.getPhoneEnd());
        ordOrderInfoBo.setCouponGrantId(remoteOrderBo.getCouponGrantId());
        ordOrderInfoBo.setCouponGrantQuota(remoteOrderBo.getCouponGrantQuota());
        ordOrderInfoBo.setPassengerDetail(remoteOrderBo.getPassengerDetail());
        if (remoteOrderBo.getStartPosition() != null) {
            remoteOrderBo.getStartPosition().setTenantId(order.getTenantId());
            ordOrderInfoBo.setStartLongitude(remoteOrderBo.getStartPosition().getLongitude());
            ordOrderInfoBo.setStartLatitude(remoteOrderBo.getStartPosition().getLatitude());
            remoteOrderBo.getStartPosition().setOrderId(order.getId());
            remoteOrderBo.getStartPosition().setType(StartEndEnum.START.getCode());
            orderPositionBos.add(BeanUtil.copyProperties(remoteOrderBo.getStartPosition(), OrdPositionBo.class));
        }
        if (remoteOrderBo.getEndPosition() != null) {
            remoteOrderBo.getEndPosition().setTenantId(order.getTenantId());
            ordOrderInfoBo.setEndLongitude(remoteOrderBo.getEndPosition().getLongitude());
            ordOrderInfoBo.setEndLatitude(remoteOrderBo.getEndPosition().getLatitude());

            remoteOrderBo.getEndPosition().setOrderId(order.getId());
            remoteOrderBo.getEndPosition().setType(StartEndEnum.END.getCode());
            orderPositionBos.add(BeanUtil.copyProperties(remoteOrderBo.getEndPosition(), OrdPositionBo.class));
        }
        // 订单关联信息
        ordOrderInfoService.insertByBo(ordOrderInfoBo);
        // 订单位置信息
        orderPositionService.insertByBos(orderPositionBos);

        // 将订单加入订单抢单队列中
        boolean robFlag = Objects.equals(order.getShowed(), IsYesEnum.YES.getCode());
        if (robFlag) {
            // 订单没有线路，不参与自动抢单
            if (ObjectUtils.isNull(order.getLineId()) || order.getLineId() == 0) {
                robFlag = false;
            }

            // 司机代下单、扫码下单、订单转卖、运力代下单（已指定接单司机），不参与自动抢单
            if (Objects.equals(order.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())) {
                robFlag = false;
            } else if (Objects.equals(order.getCreateModel(), CreateModelEnum.AGENT_ORDER.getCode()) && ObjectUtils.isNotNull(order.getDriverId()) && order.getDriverId() > 0) {
                robFlag = false;
            } else if (Objects.equals(order.getCreateModel(), CreateModelEnum.PASSENGER_QRCODE.getCode())) {
                robFlag = false;
            } else if (Objects.equals(order.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())) {
                robFlag = false;
            }
        }
        if (robFlag) {
            OrderRobEvent orderRobEvent = ordOrderHelper.buildOrderRobEvent(order, remoteOrderBo.getStartPosition(), remoteOrderBo.getEndPosition());
            orderRobProducer.sendMessage(orderRobEvent);
        }
    }

    @Async
    public void afterDispatch(boolean result, OrdOrder order, OrdOrderDispatchBo dispatchBo, OrdOrderOperateEvent operateEvent, OrdOrderDispatchBo.DispatchInfo dispatchInfo) {
        // 操作记录
        TenantHelper.setDynamic(order.getTenantId());
        if (result) {
            // 投保
            ExceptionUtil.ignoreEx(() -> {
                ordOrderInsureService.handleOrderInsure(order, FileTypeEnum.INSURE.getCode(), null);
            });

            ExceptionUtil.ignoreEx(() -> {
                // 推送消息
                pushDispatchMsg(order, dispatchBo.getType(), dispatchInfo);
            });

            ExceptionUtil.ignoreEx(() -> {
                // 给司机发送短信
                if (!Objects.equals(order.getCreateModel(), CreateModelEnum.DRIVER_ORDER.getCode())){
                    ordOrderHelper.sendMessageAsync(order.getId(), order.getDriverId(), order.getOrderNo(), SmsUseEnum.ORDER_NEW);
                }
                // 订单改派不需要再给乘客发送短信
                if (order.getPlatformCode().equals(PlatformEnum.SELF.getCode()) && !Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())) {
                    log.info("发送短信：{}-{}", order.getId(), order.getPassengerPhone());
                    // 给乘客发短信
                    OrdPositionVo endPos = orderPositionService.queryByOrderIdAndType(order.getId(), StartEndEnum.END.getCode());
                    if (endPos != null) {
                        ordOrderHelper.sendMessageAsync(SmsUseEnum.ORDER_RECEIVED, order.getPassengerPhone(), order.getEarliestTime(), endPos.getShortAddr());
                    }
                }
            });

            ExceptionUtil.ignoreEx(() -> {
                // 美团订单接单需要通知美团
                boolean notifyMt = (!DispatchTypeEnum.CHANGE.getCode().equals(dispatchBo.getType())) && PlatformEnum.MT.getCode().equals(order.getPlatformCode());
                if (notifyMt) {
                    ExceptionUtil.ignoreEx(() -> {
                        RemotePlatformApiResponseVo response = this.thirdPlatformApi(order, operateEvent.getOperateType());
                        if (SuccessFailEnum.SUCCESS.getCode().equals(response.getStatus())) {
                            operateEvent.setStatus(SuccessFailEnum.SUCCESS.getCode());
                        } else {
                            operateEvent.setStatus(SuccessFailEnum.FAIL.getCode());
                        }
                        operateEvent.setResponseJson(JsonUtils.toJsonString(response));
                    }, "第三方通知异常", () -> ordOrderOperateProducer.sendMessage(operateEvent));
                }
            });

            ExceptionUtil.ignoreEx(() -> {
                // 聊天会话改派司机
                if (Objects.equals(dispatchBo.getType(), DispatchTypeEnum.CHANGE.getCode())) {
                    remoteImService.dispatch(order.getId(), dispatchBo.getDispatchInfo().getDispatchDriverId());
                }
            });

            // 发送websocket消息，通知司机订单取消
            if (ObjectUtils.isNotNull(order.getDriverId()) && order.getDriverId() > 0) {
                this.asyncSendWebSocketMessage(order.getId(), order.getDriverId());
            }
        }
        TenantHelper.clearDynamic();
    }

    /**
     * 推送订单派单消息
     * @param order
     * @param dispatchType
     */
    public void pushDispatchMsg(OrdOrder order, String dispatchType, OrdOrderDispatchBo.DispatchInfo dispatchInfo) {
        OrdPositionVo endPos = orderPositionService.queryByOrderIdAndType(order.getId(), StartEndEnum.END.getCode());
        if (endPos == null) return;

        // 新订单分配司机
        if (DispatchTypeEnum.ASSIGN.getCode().equals(dispatchType)) {
            PushTypeEnum pushType = PushTypeEnum.BACKEND_ASSIGN_ORDER;
            String body = pushType.formatContent(DateUtil.formatDateTime(order.getEarliestTime()), endPos.getCity());
            pushMsgToDriver(order.getTenantId(), pushType, order.getDriverId(), pushType.formatTitle(), body, pushType.formatUrl(order.getId()));
        } else if (DispatchTypeEnum.CHANGE.getCode().equals(dispatchType)) {
            // 改派 老司机通知
            PushTypeEnum pushType = PushTypeEnum.BACKEND_REASSIGN_ORDER_ORIGINAL_DRIVER;
            String body = pushType.formatContent(DateUtil.formatDateTime(order.getEarliestTime()), endPos.getCity());
            pushMsgToDriver(order.getTenantId(), pushType, dispatchInfo.getOriginalDriverId(), pushType.formatTitle(), body, pushType.formatUrl(order.getId()));
            // 改派 新司机提醒
            pushType = PushTypeEnum.BACKEND_REASSIGN_ORDER_NEW_DRIVER;
            body = pushType.formatContent(DateUtil.formatDateTime(order.getEarliestTime()), endPos.getCity());
            pushMsgToDriver(order.getTenantId(), pushType, dispatchInfo.getDispatchDriverId(), pushType.formatTitle(), body, pushType.formatUrl(order.getId()));
        } else if (DispatchTypeEnum.DRIVER_AUTO_ROB.getCode().equals(dispatchType)) {
            // 司机自动抢单
            PushTypeEnum pushType = PushTypeEnum.DRIVER_AUTO_GRAB_SUCCESS;
            String body = pushType.formatContent(DateUtil.formatDateTime(order.getEarliestTime()), endPos.getCity());
            pushMsgToDriver(order.getTenantId(), pushType, order.getDriverId(), pushType.formatTitle(), body, pushType.formatUrl(order.getId()));
        } else if (DispatchTypeEnum.AGENT_ROB.getCode().equals(dispatchType)) {
            // 代理商自动抢单
            PushTypeEnum pushType = PushTypeEnum.AGENT_AUTO_GRAB_SUCCESS;
            String body = pushType.formatContent(DateUtil.formatDateTime(order.getEarliestTime()), endPos.getCity());
            pushMsgToDriver(order.getTenantId(), pushType, order.getDriverId(), pushType.formatTitle(), body, pushType.formatUrl(order.getId()));
        }
    }

    public RemotePlatformApiResponseVo thirdPlatformApi(OrdOrder order, String operateType) {
        // 自营平台数据不需要调用第三方接口
        if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.MT.getCode())) {
            // TODO-NEW 响应数据放在MQ
            return mtApiOperation(operateType, order);
        }
        return null;
    }


    /**
     * 处理美团平台不同的操作类型
     *
     * @param operateType
     * @param order
     * @return
     */
    private RemotePlatformApiResponseVo mtApiOperation(String operateType, OrdOrder order) {
        // 司机接单 | 出发去接乘客 | 到达起点 | 乘客上车 | 取消订单 | 行程结束
        if (Objects.equals(operateType, OperateTypeEnum.RECEIVE.getCode()) ||
                Objects.equals(operateType, OperateTypeEnum.PICK.getCode()) ||
                Objects.equals(operateType, OperateTypeEnum.PICK_START.getCode()) ||
                Objects.equals(operateType, OperateTypeEnum.INTO.getCode()) ||
                Objects.equals(operateType, OperateTypeEnum.CANCEL.getCode()) ||
                Objects.equals(operateType, OperateTypeEnum.FINISH.getCode())) {
            // 推送订单状态
            return platformOrderApiComponent.pushOrderStatus(order, operateType);
        }
        return null;
    }

    public void afterPayment(String tenantId, Long orderId, Date earliestTime, Long driverId) {
        PushTypeEnum pushType = PushTypeEnum.ORDER_PASSENGER_PAY;
        OrdPositionVo endPos = orderPositionService.queryByOrderIdAndType(orderId, StartEndEnum.END.getCode());
        if (endPos != null) {
            String body = pushType.formatContent(DateUtil.formatDateTime(earliestTime), endPos.getCity());
            pushMsgToDriver(tenantId, pushType, driverId, pushType.formatTitle(), body, pushType.formatUrl(orderId));
        }
    }

    public void afterHandle(String tenantId, Long orderId, String status, Long driverId, Long passengerId) {
        if (OrderStatusEnum.PICK_START.getCode().equals(status)) {
            PushTypeEnum pushType = PushTypeEnum.ORDER_PASSENGER_CLICK;
            pushMsgToDriver(tenantId, pushType, driverId, pushType.formatTitle(), pushType.formatContent(), null);

            // 提醒乘客小心点击出发
            RemoteImMsgInfo info = RemoteImMsgInfo.buildDiyInfo(orderId, UserTypeEnum.PASSENGER_USER.getUserType(), passengerId, DiyMessageEnum.ORDER_START);
            remoteImService.sengMessage(info);
        }
    }

    public boolean afterCancelOrder(OrdOrder order, RemoteOrderCancelBo cancelBo) {
        boolean flag;
        // 修改订单状态
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, order.getId())
                // 保证同一时刻内 取消订单状态不会被其他操作覆盖
                .set(OrdOrder::getVersion, order.getVersion() + 1)
                .set(OrdOrder::getStatus, OrderStatusEnum.CANCEL.getCode())
                .set(OrdOrder::getCancelUserType, cancelBo.getUserType())
                .set(OrdOrder::getCancelType, cancelBo.getCancelType())
                .set(OrdOrder::getCancelTime, DateUtils.getNowDate())
                // 第三方订单状态
                .set(StringUtils.isNotBlank(cancelBo.getThirdStatus()), OrdOrder::getThirdStatus, cancelBo.getThirdStatus());

        // 修改订单关联表信息
        LambdaUpdateWrapper<OrdOrderInfo> infoUpdateWrapper = new LambdaUpdateWrapper<>();
        infoUpdateWrapper.eq(OrdOrderInfo::getOrderId, order.getId())
                .set(OrdOrderInfo::getCancelUserId, cancelBo.getUserId())
                .set(OrdOrderInfo::getCancelRemark, cancelBo.getCancelRemark())
                .set(OrdOrderInfo::getUpdateTime, DateUtils.getNowDate());

        // 订单已完单，在进行取消操作 - 视为客诉
        if (Objects.equals(order.getStatus(), OrderStatusEnum.FINISH.getCode())) {
            // 客诉
            cancelBo.setComplain(IsYesEnum.YES.getCode());
            cancelBo.setComplainPrice(order.getOrderPrice());
            cancelBo.setComplainType(ComplainTypeEnum.OTHER.getCode());
            cancelBo.setComplainRemark(PlatformCodeEnum.getInfoByCode(cancelBo.getPlatformCode()) + "客诉");
            // 客诉时间
            cancelBo.setComplainTime(DateUtils.getNowDate());
            flag = ordOrderComplainHelper.complain(cancelBo, order);
        } else {
            // 返利状态 - 取消
            updateWrapper.set(OrdOrder::getRebateStatus, RebateStatusEnum.CANCEL.getCode());
        }

        // 先完成客诉，在更新订单数据，否则在客诉时校验订单状态会报错
        flag = ordOrderMapper.update(updateWrapper) > 0;
        if (flag) {
            // 异步操作
            scheduledExecutorService.submit(() -> asyncAfterCancelOrder(order));
        }
        return flag;
    }

    @Async
    public void asyncAfterCancelOrder(OrdOrder order) {
        TenantHelper.setDynamic(order.getTenantId());
        /*ExceptionUtil.ignoreEx(() -> {
            // 自营解绑虚拟号
            if (PlatformEnum.SELF.getCode().equals(order.getPlatformCode())) {
                RemoteOrderInfoVo info = ordCacheManager.getOrderSubInfoByOrderId(order.getId());
                if (StrUtil.isNotBlank(info.getVirtualPhone())) {
                    // 解绑旧关系
                    RemoteOrderDriverVo orderDriverInfo = ordCacheManager.getOrderDriverInfoByOrderId(order.getId());
                    if (orderDriverInfo != null) {
                        virtualPhoneService.unbindAxb(orderDriverInfo.getDriverPhone(), info.getVirtualPhone(), order.getPassengerPhone(), String.valueOf(order.getId()));
                    }
                    //virtualPhoneService.unbindAxb(order.getPassengerPhone(), info.getVirtualPhone(), null, String.valueOf(order.getId()));
                }
            }
        }, "解绑虚拟号异常");*/

        RemotePositionVo endPos = ordCacheManager.getOrderPositionByOrderIdAndType(order.getId(), StartEndEnum.END.getCode());
        ExceptionUtil.ignoreEx(() -> {
            // 客诉的不发短信
            if (!OrderStatusEnum.FINISH.getCode().equals(order.getStatus())) {
                if (PlatformEnum.SELF.getCode().equals(order.getPlatformCode())) {
                    ordOrderHelper.sendMessageAsync(SmsUseEnum.ORDER_CANCEL, order.getPassengerPhone(), order.getEarliestTime(), endPos.getShortAddr());
                }
                // 发短信
                if (ArithUtils.isNotNull(order.getDriverId())) {
                    RemoteOrderDriverVo orderDriverVo = ordCacheManager.getOrderDispatchDriverInfoByOrderId(order.getId());
                    if (ObjectUtil.isNotNull(orderDriverVo)) {
                        ordOrderHelper.sendMessageAsync(SmsUseEnum.ORDER_CANCEL, orderDriverVo.getDriverPhone(), order.getEarliestTime(), endPos.getShortAddr());
                    }
                }
            }
        }, "短信发送失败");

        ExceptionUtil.ignoreEx(() -> {
            // 订单取消，需要退保
            if (Objects.equals(order.getInsureStatus(), InsureStatusEnum.INSURED.getCode())) {
                ordOrderInsureService.handleOrderInsure(order, FileTypeEnum.CANCEL_INSURE.getCode(), null);
            }
        }, "退保异常");

        ExceptionUtil.ignoreEx(() -> {
            // 注意客诉不要推送
            if (!OrderStatusEnum.FINISH.getCode().equals(order.getStatus())) {
                // 推送消息
                PushTypeEnum pushType = LoginHelper.getUserType() != UserTypeEnum.SYS_USER ?
                        PushTypeEnum.ORDER_PASSENGER_CANCEL :
                        PushTypeEnum.ORDER_BACKEND_CANCEL;
                if (endPos != null) {
                    String body = pushType.formatContent(OrderUtils.getPassengerName(order.getPassengerPhone()), endPos.getCity());
                    pushMsgToDriver(order.getTenantId(), pushType, order.getDriverId(), pushType.formatTitle(), body, null);
                }
            }
        }, "推送异常");
        TenantHelper.clearDynamic();
    }

    /**
     * 发起退款
     * @param order
     * @return
     */
    public boolean refund(OrdOrder order) {
        if (Objects.equals(order.getPlatformCode(), PlatformCodeEnum.SELF.getCode())) {
            // 是否退款 两个条件：1、订单状态支付成功 2、支付流水状态为成功
            if (order.getPayStatus().equals(PaymentStatusEnum.SUCCESS.getCode())) {
                List<RemotePaymentRecordVo> recordVos = remotePaymentRecordService.queryList(JoinEnum.ORDER.getCode(), Arrays.asList(order.getId()));
                List<RemotePaymentRecordVo> sortedRecVos = recordVos.stream().sorted(Comparator.comparing(RemotePaymentRecordVo::getCreateTime, Comparator.reverseOrder())).toList();
                List<RemotePaymentRecordVo> payRecVos = sortedRecVos.stream()
                        // 同一支付方式
                        .filter(e -> order.getPayMode().equals(e.getPaymentType()))
                        // 支付成功的
                        .filter(e -> PaymentStatusEnum.SUCCESS.getCode().equals(e.getStatus()))
                        // 乘客支付记录
                        .filter(e -> RecordTypeEnum.PASSENGER_PAYMENT.getCode().equals(e.getType()))
                        // 非回调记录
                        .filter(e -> ObjectUtils.isNull(e.getNotify()) || IsYesEnum.NO.getCode().equals(e.getNotify()))
                        .toList();
                if (CollUtil.isEmpty(payRecVos)) {
                    throw new ServiceException("数据异常，未找到对应的支付流水");
                }
                // 获取最新的一条
                RemotePaymentRecordVo remotePaymentFlowVo = payRecVos.get(0);
                if (IsYesEnum.YES.getCode().equals(remotePaymentFlowVo.getIsRefund())) {
                    log.info("订单已退款：{}-{}", remotePaymentFlowVo.getId(), remotePaymentFlowVo.getOutBizNo());
                    return true;
                }

                RefundBo refundBo = new RefundBo(PaymentChannelEnum.getByType(PaymentTypeEnum.getByCode(remotePaymentFlowVo.getPaymentType())));
                refundBo.setAppId(remotePaymentFlowVo.getAppId());
                refundBo.setTenantId(order.getTenantId());
                refundBo.setBizNo(remotePaymentFlowVo.getOutBizNo());
                refundBo.setAmount(remotePaymentFlowVo.getAmount());
                refundBo.setRefundNo(order.getOrderNo());

                PaymentRecordEvent recordEvent = createRecordEvent(remotePaymentFlowVo);
                recordEvent.setParamsJson(JsonUtils.toJsonString(refundBo));
                OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(order.getId(), OperateTypeEnum.REFUND.getCode(), JsonUtils.toJsonString(refundBo));
                try {
                    RefundVo refund = paymentService.refund(refundBo);
                    recordEvent.setResponseJson(JsonUtils.toJsonString(refund));
                    operateEvent.setResponseJson(JsonUtils.toJsonString(refund));
                    return true;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    log.error("退款失败：{}", e.getMessage());
                    recordEvent.setResponseJson(e.getMessage());
                    operateEvent.setResponseJson(e.getMessage());
                    ExceptionUtil.ignoreEx(() -> {
                        if (emergencyEmail != null) {
                            MailUtils.sendText(emergencyEmail, StrUtil.format("订单{}发起退款异常", order.getId()), Arrays.toString(e.getStackTrace()));
                        }
                    });
                    return false;
                } finally {
                    PaymentRecordProducer.sendMessage(recordEvent);
                    ordOrderOperateProducer.sendMessage(operateEvent);
                }
            }
        }
        return true;
    }

    private PaymentRecordEvent createRecordEvent(RemotePaymentRecordVo remotePaymentFlowVo) {
        PaymentRecordEvent record = new PaymentRecordEvent();
        record.setTenantId(remotePaymentFlowVo.getTenantId());
        record.setJoinTable(JoinEnum.ORDER.getCode());
        record.setJoinId(remotePaymentFlowVo.getJoinId());
        record.setJoinNo(remotePaymentFlowVo.getJoinNo());
        record.setPaymentType(remotePaymentFlowVo.getPaymentType());
        record.setAppId(remotePaymentFlowVo.getAppId());
        record.setMchId(remotePaymentFlowVo.getMchId());
        record.setOutBizNo(remotePaymentFlowVo.getOutBizNo());
        record.setType(RecordTypeEnum.PASSENGER_REFUND.getCode());
        record.setAmount(remotePaymentFlowVo.getAmount());
        record.setStatus(PaymentStatusEnum.ING.getCode());
        record.setDirection(DirectionEnum.OUT.getCode());
        return record;
    }

    /**
     * 行程开始后处理
     *
     * @param order 订单
     * @param thirdStatus 第三方订单状态
     * @return 是否成功
     */
    public boolean afterTripStart(OrdOrder order, String thirdStatus) {
        // 修改订单状态
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, order.getId())
                // 版本号
                .eq(OrdOrder::getVersion, order.getVersion())
                .set(OrdOrder::getVersion, order.getVersion() + 1)
                .set(StringUtils.isNotBlank(thirdStatus), OrdOrder::getThirdStatus, thirdStatus)
                .set(OrdOrder::getStatus, OrderStatusEnum.ING.getCode())
                .set(OrdOrder::getTripStartTime, DateUtils.getNowDate());

        boolean flag = ordOrderMapper.update(updateWrapper) > 0;

        // 推送消息
        PushTypeEnum pushType = PushTypeEnum.ORDER_START_TRIP;
        pushMsgToDriver(order.getTenantId(), pushType, order.getDriverId(), pushType.formatTitle(), pushType.formatContent(), null);

        return flag;
    }

    /**
     * 订单完单后处理
     *
     * @param order 订单
     * @param thirdStatus 第三方订单状态
     * @return 是否成功
     */
    public boolean afterTripEnd(OrdOrder order, String thirdStatus) {
        boolean flag;
        // 计算服务时长
        long duration = this.calculateOrderServiceTime(order.getTripStartTime());
        Date finishTime = DateUtils.getNowDate();

        // 修改订单关联信息
        LambdaUpdateWrapper<OrdOrderInfo> infoUpdateWrapper = new LambdaUpdateWrapper<>();
        infoUpdateWrapper.eq(OrdOrderInfo::getOrderId, order.getId())
                .set(OrdOrderInfo::getDuration, duration)
                .set(OrdOrderInfo::getUpdateTime, DateUtils.getNowDate());
        ordOrderInfoMapper.update(infoUpdateWrapper);

        // 修改订单信息
        LambdaUpdateWrapper<OrdOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OrdOrder::getId, order.getId())
                .eq(OrdOrder::getVersion, order.getVersion())
                .set(OrdOrder::getVersion, order.getVersion() + 1)
                .set(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .set(OrdOrder::getFinishTime, finishTime)
                .set(OrdOrder::getRebateStatus, RebateStatusEnum.ING.getCode())
                .set(OrdOrder::getResellRebateStatus, RebateStatusEnum.ING.getCode())
                .set(StringUtils.isNotBlank(thirdStatus), OrdOrder::getThirdStatus, thirdStatus);
        flag = ordOrderMapper.update(updateWrapper) > 0;

        //记录订单轨迹
        scheduledExecutorService.schedule(() -> ordTrackService.saveOrderTrackLocation(order.getId(),order.getTenantId()), 0, TimeUnit.SECONDS);
        if (flag) {
            // 开始返利
            RemoteRebateBo rebateBo = orderOrderRebateService.createRemoteRebateVo(order);
            flag = remoteDrvWalletService.orderFreezeAdd(rebateBo);

            // 订单转卖返利
            if (Objects.equals(order.getCreateModel(), CreateModelEnum.RESELL_ORDER.getCode())
                    && order.getResellDriverId() != null && order.getResellDriverId() > 0) {
                rebateBo.setResellDriverId(order.getResellDriverId());
                rebateBo.setResellAgentId(order.getResellAgentId());
                RemoteDriverVo driverVo = powCacheManager.getDriverInfoById(order.getResellDriverId());
                // 卖单司机收益（订单金额 - 转卖后司机接单金额 - 订单金额 * 订单转卖服务费比例）
                long resellDriverProfit = order.getOrderPrice() -  order.getResellDriverPrice() - ArithUtils.profitUseBigDecimal(order.getOrderPrice(), driverVo.getResellServiceRate());
                rebateBo.setResellDriverProfit(resellDriverProfit);
                rebateBo.setResellDriverName(driverVo.getName());
                rebateBo.setResellDriverPhone(driverVo.getPhone());
                flag = remoteDrvWalletService.orderFreezeAddForResell(rebateBo);
            }


            //邀请有奖返利
            if (ordOrderHelper.isInviteRewardBoolean()) {
                RemoteOrderRateVo remoteOrderRateVo = ordCacheManager.getOrderRateInfoByOrderIdAndRateType(order.getId(), RateTypeEnum.INVITE_AGENT.getCode());
                if (ObjectUtils.isNotNull(remoteOrderRateVo)) {
                    String inviteType;
                    if (ObjectUtils.isNotNull(order.getInviteDriverId())){
                        inviteType = InviteTypeEnum.DRIVER_INVITE.getCode();
                    }else {
                        inviteType= InviteTypeEnum.AGENT_INVITE.getCode();
                    }
                    RemoteInviteRecordVo build = RemoteInviteRecordVo.builder()
                            .tenantId(order.getTenantId())
                            .passengerId(order.getPassengerId())
                            .agentId(remoteOrderRateVo.getUserId())
                            .driverId(order.getDriverId())
                            .orderId(remoteOrderRateVo.getOrderId())
                            .inviteType(inviteType)
                            .rewardType(RewardTypeEnum.NEW_USER_REWARD.getCode())
                            .rewardRate(remoteOrderRateVo.getRate())
                            .rewardPrice(remoteOrderRateVo.getAmount())
                            .build();
                    remoteInviteRecordService.insertByBo(build);
                }
            }
        }

        // 订单状态变化事件
        asyncSendOrderStatusChangeEvent(order.getId());
        return flag;
    }

    /**
     * 更新优惠券使用状态
     */
    public void updateCouponGrantStatus(OrdOrder order, String couponStatus){
        LambdaQueryWrapper<OrdOrderInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrderInfo::getOrderId, order.getId())
                .orderByDesc(OrdOrderInfo::getCreateTime)
                .last(Constants.LIMIT_ONE);
        OrdOrderInfo ordOrderInfo = ordOrderInfoMapper.selectOne(queryWrapper);
        if (ObjectUtils.isNotNull(ordOrderInfo) && ObjectUtils.isNotNull(ordOrderInfo.getCouponGrantId())
                && ordOrderInfo.getCouponGrantId() > 0 && ObjectUtils.isNotNull(order.getPassengerId())) {
            RemoteCouponGrantBo remoteCouponGrantBo = new RemoteCouponGrantBo();
            remoteCouponGrantBo.setPassengerId(order.getPassengerId());
            remoteCouponGrantBo.setUsingStatus(couponStatus);
            remoteCouponGrantBo.setOrderId(order.getId());
            remoteCouponGrantBo.setOrderNo(order.getOrderNo());
            remoteCouponGrantBo.setId(ordOrderInfo.getCouponGrantId());
            remoteCouponGrantService.updateStatus(remoteCouponGrantBo);
        }
    }

    /**
     * 计算订单服务时长
     *
     * @param tripStartTime 订单开始时间
     * @return 订单服务时长
     */
    public Long calculateOrderServiceTime(Date tripStartTime) {
        if (tripStartTime != null) {
            return DateUtil.betweenMs(tripStartTime, DateUtils.getNowDate());
        }
        return 0L;
    }


    /**
     * 推送消息
     */
    public void pushMsgToDriver(String tenantId, PushTypeEnum pushType, Long driverId, String title, String body, String url) {
        TenantHelper.setDynamic(tenantId);
        try {
            PushEvent event = new PushEvent(pushType);
            event.setTitle(title);
            event.setBody(body);
            event.setUrl(url);
            event.setTenantId(tenantId);

            // 接收人
            event.getReceiverParam().setUserType(UserTypeEnum.DRIVER_USER.getUserType());
            event.getReceiverParam().setUserId(driverId);

            // 语音
            if (pushType.getVoice() != null) {
                RemoteDictDataVo dataVo = remoteDictService.selectDictDataByTypeAndLabel(VoiceConstant.SYSTEM_VOICE_BROADCAST, pushType.getVoice());
                if (dataVo != null) {
                    PushEvent.GtParam gtParam = event.getGtParam();
                    gtParam.setPayload(dataVo.getDictValue());
                    event.setGtParam(gtParam);
                }
            }

            List<String> cid = remoteImService.getCid(UserTypeEnum.DRIVER_USER.getUserType(), driverId);
            event.setCids(cid);
            PushMsgProducer.sendMessage(event);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("订单相关消息推送异常");
        } finally {
            TenantHelper.clearDynamic();
        }
    }

    /**
     * 发送websocket消息，通知司机订单状态变更
     *
     * @param orderId 订单id
     * @param driverId 司机id
     */
    public void asyncSendWebSocketMessage(Long orderId, Long driverId) {
        scheduledExecutorService.schedule(() -> {
            //发送websocket，通知司机订单状态变更
            WebSocketMessageWrapper<OrderMessage> messageWrapper = new WebSocketMessageWrapper<>();
            OrderMessage orderMessage = OrderMessage.builder()
                    .messageType(OrderMessageTypeEnum.ORDER_STATUS.getCode())
                    .id(String.valueOf(orderId))
                    .build();
            messageWrapper.setType(WebSocketTypeEnum.ORDER.getCode());
            messageWrapper.setReceiverUserType(UserTypeEnum.DRIVER_USER.getUserType());
            messageWrapper.setReceiverId(String.valueOf(driverId));
            messageWrapper.setData(orderMessage);
            remoteWebSocketService.sendMessage(messageWrapper);
        }, 0, TimeUnit.SECONDS);
    }
    private static OrdOrderStatusChangeEvent getStatusChangeEvent(OrdOrder order) {
        OrdOrderStatusChangeEvent event = new OrdOrderStatusChangeEvent();
        event.setOrderId(order.getId());
        event.setPassengerId(order.getPassengerId());
        event.setPassengerPhone(order.getPassengerPhone());
        event.setOrderNo(order.getOrderNo());
        event.setTenantId(order.getTenantId());
        event.setAgentId(order.getAgentId());
        event.setDriverId(order.getDriverId());
        event.setProductCode(order.getProductCode());
        event.setOrderPrice(order.getOrderPrice());
        event.setPayPrice(order.getPayPrice());
        event.setStatus(order.getStatus());
        event.setPayStatus(order.getPayStatus());
        event.setPayMode(order.getPayMode());
        event.setPayTime(order.getPayTime());
        event.setPayNo(order.getPayNo());
        event.setFinishTime(order.getFinishTime());
        event.setThirdStatus(order.getThirdStatus());
        event.setComplain(order.getComplain());
        return event;
    }
    /**
     * 订单状态变化事件
     */
    public void asyncSendOrderStatusChangeEvent(Long  orderId) {
        scheduledExecutorService.schedule(() -> {
            log.debug("asyncSendOrderStatusChangeEvent orderId:{}", orderId);
            OrdOrder ordOrder = ordOrderMapper.selectById(orderId);
            OrdOrderStatusChangeEvent event = getStatusChangeEvent(ordOrder);
            ordOrderStatusChangeProducer.sendMessage(event);
        }, 0, TimeUnit.SECONDS);
    }
}
