package com.feidi.xx.cross.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.json.utils.JsonUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.payment.domain.payment.bo.QueryBo;
import com.feidi.xx.common.payment.domain.payment.vo.QueryResultVo;
import com.feidi.xx.common.payment.strategy.IPaymentService;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.common.enums.order.OperateTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.order.YesOrNoEnum;
import com.feidi.xx.cross.common.helper.OrdOrderOperateHelper;
import com.feidi.xx.cross.common.mq.event.OrdOrderOperateEvent;
import com.feidi.xx.cross.common.mq.producer.OrdOrderOperateProducer;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.finance.api.RemotePaymentRecordService;
import com.feidi.xx.cross.finance.api.domain.bo.RemotePaymentRecordBo;
import com.feidi.xx.cross.finance.api.domain.vo.RemotePaymentRecordVo;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.bo.RemoteOrderPaymentBo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderInfoVo;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChain;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainContext;
import com.feidi.xx.cross.order.chain.cancel.OrderCancelChainResult;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChain;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainContext;
import com.feidi.xx.cross.order.chain.operate.OrderOperateChainResult;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChain;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainContext;
import com.feidi.xx.cross.order.chain.payment.OrderPaymentChainResult;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChain;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainContext;
import com.feidi.xx.cross.order.chain.place.OrderPlaceChainResult;
import com.feidi.xx.cross.order.domain.*;
import com.feidi.xx.cross.order.domain.bo.OrdOrderCommentBo;
import com.feidi.xx.cross.order.domain.bo.OrdUpdateRemarkBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPaymentBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPlaceBo;
import com.feidi.xx.cross.order.domain.vo.*;
import com.feidi.xx.cross.order.mapper.*;
import com.feidi.xx.cross.order.service.IOrdEvaluationsService;
import com.feidi.xx.cross.order.service.IOrdOrderMbrService;
import com.feidi.xx.cross.passenger.api.RemotePassengerService;
import com.feidi.xx.cross.power.api.RemoteDriverService;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrdOrderMbrServiceImpl implements IOrdOrderMbrService {

    private final OrdOrderMapper baseMapper;

    private final OrdPositionMapper positionMapper;
    private final OrdOrderInfoMapper ordOrderInfoMapper;
    private final OrderPlaceChain placeChain;

    private final IPaymentService paymentService;

    private final OrderOperateChain operateChain;

    private final RemoteOrderService remoteOrderService;

    private final OrderCancelChain cancelChain;

    private final OrderPaymentChain paymentChain;

    private final OrdOperateMapper operateMapper;

    private final OrdOrderOperateProducer operateProducer;

    @DubboReference
    private final RemotePassengerService remotePassengerService;

    @DubboReference
    private final RemotePaymentRecordService paymentRecordService;

    @DubboReference
    private final RemoteDriverService remoteDriverService;

    @DubboReference
    private final RemoteImService remoteImService;

    private final OrdCacheManager ordCacheManager;

    private final IOrdEvaluationsService ordEvaluationsService;

    private final OrdDriverEvaluationMapper ordDriverEvaluationMapper;

    /**
     * 获取订单详情
     *
     * @param id 订单ID
     * @return 订单详情VO
     */
    @Override
    public OrdOrderMbrVo queryById(Long id) {
        // 获取订单信息
        OrdOrder order = baseMapper.selectById(id);
        if (order == null) {
            log.info("订单ID不存在:{} ", id);
            return null;
        }
        return getInfo(order);
    }

    @Override
    public OrdOrderMbrVo queryByCode(String code) {
        // 获取订单信息
        OrdOrder order = baseMapper.getByCode(code, LoginHelper.getUserPhone());
        if (order == null) {
            log.info("订单code不存在:{} ", code);
            return null;
        }
        return getInfo(order);
    }

    private OrdOrderMbrVo getInfo(OrdOrder order) {
        String virtualPhone = null;
        RemoteOrderInfoVo info = ordCacheManager.getOrderSubInfoByOrderId(order.getId());
        if (info != null) {
            virtualPhone = info.getVirtualPhone();
        }

        OrdOrderMbrVo orderVo = MapstructUtils.convert(order, OrdOrderMbrVo.class);
        orderVo.setVirtualPhone(virtualPhone);
        List<OrdOrderTrackLocationCacheVo> orderTrackLocations = ordCacheManager.getAllOrderTrackLocationByOrderId(order.getId());
        if (CollUtil.isNotEmpty(orderTrackLocations)) {
            orderTrackLocations = orderTrackLocations.stream().sorted(Comparator.comparing(OrdOrderTrackLocationCacheVo::getPositionTime).reversed()).toList();
            orderVo.setTrackLocationCacheVoList(orderTrackLocations);
        }
        // 获取位置信息
        List<OrdPosition> cxPositions = positionMapper.selectList(new LambdaQueryWrapper<OrdPosition>()
                .eq(OrdPosition::getOrderId, order.getId()));
        List<OrdPositionVo> cxPositionVos = BeanUtils.copyToList(cxPositions, OrdPositionVo.class);
        if (cxPositionVos != null) {
            for (OrdPositionVo cxPositionVo : cxPositionVos) {
                if (Objects.equals(cxPositionVo.getType(), StartEndEnum.START.getCode())) {
                    orderVo.setStartPositionVo(cxPositionVo);
                } else if (Objects.equals(cxPositionVo.getType(), StartEndEnum.END.getCode())) {
                    orderVo.setEndPositionVo(cxPositionVo);
                }
            }
        }
        // 获取司机车辆信息
        if (order.getDriverId() > 0) {
            RemoteDriverVo driverAndCarInfo = remoteDriverService.getDriverAndCar(order.getDriverId());
            if (driverAndCarInfo != null) {
                // 没有调度号使用真实手机号
                // driverAndCarInfo.setPhone(StrUtil.isNotBlank(virtualPhone) ? virtualPhone : driverAndCarInfo.getPhone());
                orderVo.setDriverVo(driverAndCarInfo);
            }
            OrdDriverEvaluation ordDriverEvaluation = ordDriverEvaluationMapper.queryByDriverId(order.getDriverId());
            if (ordDriverEvaluation != null) {
                orderVo.setDriverRatings(ordDriverEvaluation.getTotalScore());
            }
        }

        //  获取操作人信息
        LambdaQueryWrapper<OrdOperate> operateLambdaQueryWrapper = new LambdaQueryWrapper<>();
        operateLambdaQueryWrapper.eq(OrdOperate::getOrderId, order.getId())
                .eq(OrdOperate::getOperateType, OperateTypeEnum.getOperateTypeByOrderStatus(order.getStatus()))
                .orderByDesc(OrdOperate::getCreateTime)
                .last("limit 1");
        OrdOperateVo ordOperateVo = operateMapper.selectVoOne(operateLambdaQueryWrapper);
        if (ordOperateVo != null) {
            orderVo.setUserType(ordOperateVo.getUserType());
        }

        // 判断按钮状态
        orderVo.setButtonStatus(this.judgeButtonStatus(orderVo));

        if (ObjectUtils.isNotNull(info)) {
            // 优惠券额度
            orderVo.setCouponGrantQuota(info.getCouponGrantQuota());
            //评价
            orderVo.setIsRated(info.getIsRated());
            if (YesOrNoEnum.YES.getCode().equals(info.getIsRated())) {
                orderVo.setEvaluation(ordEvaluationsService.queryByOrderId(order.getId()));
            }
        }

        // 未读数量
        ExceptionUtil.ignoreEx(() -> {
            Integer unreadNum = remoteImService.getUnreadNum(orderVo.getId());
            orderVo.setUnreadNum(unreadNum);
        });
        return orderVo;
    }

    /**
     * 判断按钮状态
     *
     * @param ordOrderMbrVo
     */
    private String judgeButtonStatus(OrdOrderMbrVo ordOrderMbrVo) {
        if (Objects.equals(ordOrderMbrVo.getStatus(), OrderStatusEnum.CANCEL.getCode())
                || Objects.equals(ordOrderMbrVo.getStatus(), OrderStatusEnum.FINISH.getCode())) {
            // 订单完成或已取消
            return StatusEnum.DISABLE.getCode();
        }

        if (!Objects.equals(ordOrderMbrVo.getPayStatus(), PaymentStatusEnum.SUCCESS.getCode())
                || (Objects.equals(ordOrderMbrVo.getStatus(), OrderStatusEnum.RECEIVE.getCode())
                || Objects.equals(ordOrderMbrVo.getStatus(), OrderStatusEnum.PICK.getCode()))) {
            return StatusEnum.DISABLE.getCode();
        }

        return StatusEnum.ENABLE.getCode();
    }

    /**
     * 获取订单列表
     *
     * @param bo
     * @return
     */
    @Override
    public TableDataInfo<OrdOrderMbrListVo> queryPageList(OrdOrderQueryWebBo bo) {
        try {
            if (bo.getPassengerId() == null) {
                return TableDataInfo.build(new ArrayList<>(), 0L);
            }
            IPage<OrdOrder> cxOrderPage = baseMapper.selectPage(bo.build(), new LambdaQueryWrapper<OrdOrder>()
                    .nested(l -> l.eq(OrdOrder::getPassengerId, bo.getPassengerId())
                            .or()
                            .eq(OrdOrder::getPassengerPhone, bo.getPassengerPhone()))
                    .orderByDesc(OrdOrder::getCreateTime));
            IPage<OrdOrderMbrListVo> iPage = cxOrderPage.convert(order -> BeanUtils.copyProperties(order, OrdOrderMbrListVo.class));
            List<OrdOrderMbrListVo> records = iPage.getRecords();
            if (CollUtil.isNotEmpty(records)) {
                // 批量获取位置信息
                Set<Long> orderIds = records.stream().map(OrdOrderMbrListVo::getId).collect(Collectors.toSet());
                // 优惠券
                Map<Long, OrdOrderInfo> id2infoMap = new HashMap<>();
                if (CollUtil.isNotEmpty(orderIds)) {
                    id2infoMap = ordOrderInfoMapper.selectList(new LambdaQueryWrapper<OrdOrderInfo>().in(OrdOrderInfo::getId, orderIds))
                            .stream().collect(Collectors.toMap(OrdOrderInfo::getId, Function.identity()));
                }
                List<OrdPosition> cxPositions = positionMapper.selectList(new LambdaQueryWrapper<OrdPosition>().in(OrdPosition::getOrderId, orderIds));
                List<OrdPositionVo> allPositions = BeanUtils.copyToList(cxPositions, OrdPositionVo.class);
                Map<Long, List<OrdPositionVo>> positionsByOrderId = allPositions.stream().collect(Collectors.groupingBy(OrdPositionVo::getOrderId));
                for (OrdOrderMbrListVo record : records) {
                    updateLocationInfo(record, positionsByOrderId.getOrDefault(record.getId(), Collections.emptyList()));
                    // 优惠券额度
                    if (id2infoMap.containsKey(record.getId())) {
                        record.setCouponGrantQuota(id2infoMap.get(record.getId()).getCouponGrantQuota());
                    }
                }
                return TableDataInfo.build(records, iPage.getTotal());
            }
            return TableDataInfo.build(new ArrayList<>(), 0L);

        } catch (IllegalArgumentException e) {
            log.warn("查询订单列表时参数无效: {}", e.getMessage());
            return TableDataInfo.build(new ArrayList<>(), 0L);
        } catch (Exception e) {
            log.error("查询订单列表时发生异常", e);
            return TableDataInfo.build(new ArrayList<>(), 0L);
        }
    }

    /**
     * 获取对应订单的起止位置
     *
     * @param record
     * @param positions
     */
    private void updateLocationInfo(OrdOrderMbrListVo record, List<OrdPositionVo> positions) {
        if (CollUtil.isNotEmpty(positions)) {
            for (OrdPositionVo cxPositionVo : positions) {
                if (Objects.equals(cxPositionVo.getType(), StartEndEnum.START.getCode())) {
                    record.setStartPositionVo(cxPositionVo);
                } else if (Objects.equals(cxPositionVo.getType(), StartEndEnum.END.getCode())) {
                    record.setEndPositionVo(cxPositionVo);
                }
            }
        }
    }

    /**
     * 获取最近的订单(下单页面)
     *
     * @param
     * @return
     */
    @Override
    public List<OrdOrderMbrListVo> queryLast(OrdOrderQueryWebBo bo) {
        // 订单信息
        List<OrdOrder> cxOrders = baseMapper.selectList(new LambdaQueryWrapper<OrdOrder>()
                .ne(OrdOrder::getStatus, OrderStatusEnum.CANCEL.getCode())
                .ne(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .nested(l -> l.eq(OrdOrder::getPassengerId, bo.getPassengerId())
                        .or()
                        .eq(OrdOrder::getPassengerPhone, bo.getPassengerPhone()))
                .orderByDesc(OrdOrder::getCreateTime)
                .last("limit 2")
        );
        List<OrdOrderMbrListVo> orders = MapstructUtils.convert(cxOrders, OrdOrderMbrListVo.class);
        if (CollUtil.isNotEmpty(orders)) {
            for (OrdOrderMbrListVo order : orders) {
                // 获取该订单的位置信息
                List<OrdPosition> positions = positionMapper.selectList(new LambdaQueryWrapper<OrdPosition>()
                        .eq(OrdPosition::getOrderId, order.getId()));
                List<OrdPositionVo> positionVos = BeanUtils.copyToList(positions, OrdPositionVo.class);
                // 遍历所有位置信息，按类型分配给起始和结束位置
                for (OrdPositionVo vo : positionVos) {
                    if (Objects.equals(vo.getType(), StartEndEnum.START.getCode())) {
                        order.setStartPositionVo(vo);
                    } else if (Objects.equals(vo.getType(), StartEndEnum.END.getCode())) {
                        order.setEndPositionVo(vo);
                    }
                }
            }
        }
        return orders;
    }

    @Override
    public Integer lastNum(OrdOrderQueryWebBo bo) {
        // 订单信息
        List<OrdOrder> cxOrders = baseMapper.selectList(new LambdaQueryWrapper<OrdOrder>()
                .ne(OrdOrder::getStatus, OrderStatusEnum.CANCEL.getCode())
                .ne(OrdOrder::getStatus, OrderStatusEnum.FINISH.getCode())
                .nested(l -> l.eq(OrdOrder::getPassengerId, bo.getPassengerId())
                        .or()
                        .eq(OrdOrder::getPassengerPhone, bo.getPassengerPhone()))
                .orderByDesc(OrdOrder::getCreateTime)
        );
        return CollUtil.isNotEmpty(cxOrders) ? cxOrders.size() : 0;
    }

    @Override
    public Long place(OrdOrderPlaceBo bo) {
        OrderPlaceChainResult result = placeChain.execute(BeanUtils.copyProperties(bo, OrderPlaceChainContext.class));
        return result.getId();
    }

    @Override
    public Boolean cancelBo(OrdOrderCancelBo bo) {
        OrderCancelChainResult result = cancelChain.execute(BeanUtils.copyProperties(bo, OrderCancelChainContext.class));
        return result.isSuccess();
    }

    @Override
    public Boolean tripStart(OrdOrderHandleBo bo) {
        bo.setStatus(OrderStatusEnum.ING.getCode());
        OrderOperateChainResult result = operateChain.execute(BeanUtils.copyProperties(bo, OrderOperateChainContext.class));
        return result.isSuccess();
    }

    @Override
    public Boolean tripEnd(OrdOrderHandleBo bo) {
        bo.setStatus(OrderStatusEnum.FINISH.getCode());
        OrderOperateChainResult result = operateChain.execute(BeanUtils.copyProperties(bo, OrderOperateChainContext.class));
        return result.isSuccess();
    }

    @Override
    public Object payment(OrdOrderPaymentBo bo) {
        OrderPaymentChainResult result = paymentChain.execute(BeanUtils.copyProperties(bo, OrderPaymentChainContext.class));
        return result.getPayInfo();
    }

    /**
     * 刷新支付状态
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void refreshPayment(Long orderId) {
        OrdOrder order = baseMapper.selectById(orderId);
        if (order != null) {
            OrdOrderOperateEvent operateEvent = OrdOrderOperateHelper.buildOrderOperateEvent(orderId, OperateTypeEnum.PAYMENT_QUERY.getCode(), null);
            try {
                PaymentTypeEnum paymentType = PaymentTypeEnum.getByCode(order.getPayMode());
                if (Arrays.asList(PaymentTypeEnum.ALI_PAY, PaymentTypeEnum.WX_PAY).contains(paymentType)) {
                    RemotePaymentRecordVo record = paymentRecordService.getPaymentRecord(LoginHelper.getTenantId(), orderId, JoinEnum.ORDER.getCode(), paymentType);
                    if (record != null && PaymentStatusEnum.ING.getCode().equals(record.getStatus())) {
                        QueryBo bo = new QueryBo();
                        bo.setTenantId(record.getTenantId());
                        bo.setChannel(PaymentChannelEnum.getByType(record.getPaymentType()));
                        bo.setAppId(record.getAppId());
                        bo.setBizNo(record.getOutBizNo());
                        QueryResultVo query = paymentService.query(bo);
                        operateEvent.setResponseJson(JsonUtils.toJsonString(query));
                        if (query.isSuccess()) {
                            RemoteOrderPaymentBo handleBo = new RemoteOrderPaymentBo();
                            handleBo.setOrderId(record.getJoinId());
                            handleBo.setPlatformCode(PlatformCodeEnum.SELF.getCode());
                            handleBo.setPayTypeEnum(record.getPaymentType());
                            // 支付成功
                            handleBo.setPayStatusEnum(PaymentStatusEnum.SUCCESS.getCode());
                            handleBo.setPayTime(query.getTradeTime() != null ? query.getTradeTime() : new Date());
                            handleBo.setUserType(UserTypeEnum.PASSENGER_USER.getUserType());
                            handleBo.setUserId(0L);
                            handleBo.setRemark("主动查询获取");
                            handleBo.setTimeStamp(DateUtils.getUnixTimeStamps());
                            remoteOrderService.paymentConfirm(handleBo);

                            // 更新流水
                            record.setStatus(PaymentStatusEnum.SUCCESS.getCode());
                            record.setTradeTime(DateUtil.formatDateTime(handleBo.getPayTime()));
                            RemotePaymentRecordBo recordBo = BeanUtils.copyProperties(record, RemotePaymentRecordBo.class);
                            paymentRecordService.savePaymentRecord(recordBo);
                        }
                    }
                }
            } finally {
                operateProducer.sendMessage(operateEvent);
            }

        }
    }

    @Override
    public List<OrdPositionVo> commonPosition() {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<OrdOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrdOrder::getPassengerId, userId)
                .orderByDesc(OrdOrder::getCreateTime)
                .last("limit 10");
        List<OrdOrder> orders = baseMapper.selectList(queryWrapper);
        //获取id
        if (CollUtil.isNotEmpty(orders)) {
            List<Long> ids = orders.stream().map(OrdOrder::getId).toList();
            LambdaQueryWrapper<OrdPosition> positionQueryWrapper = new LambdaQueryWrapper<>();
            positionQueryWrapper.in(OrdPosition::getOrderId, ids)
                    .orderByDesc(OrdPosition::getCreateTime);
            //.eq(OrdPosition::getType, position);
            List<OrdPositionVo> ordPositionVos = positionMapper.selectVoList(positionQueryWrapper);
            //根据shortAddr去重
            return ordPositionVos.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OrdPositionVo::getShortAddr))), ArrayList::new));
        }
        return List.of();
    }

    @Override
    public OrdOrderTrackLocationCacheVo driverPosition(Long driverId) {
        return ordCacheManager.getOrderTrackLocationByOrderId(driverId);
    }

    /**
     * 订单转卖
     *
     * @param bo 订单转卖参数
     * @return 订单ID
     */
    @Override
    public Long resell(OrdOrderPlaceBo bo) {
        OrderPlaceChainResult result = placeChain.execute(BeanUtils.copyProperties(bo, OrderPlaceChainContext.class));
        return result.getId();
    }

    /**
     * 修改备注
     *
     * @param bo
     */
    @Override
    public void updateRemark(OrdUpdateRemarkBo bo) {
        //判断订单是否存在
        OrdOrder order = baseMapper.selectById(bo.getOrderId());
        if (order == null) {
            log.error("updateRemark， 订单不存在");
            return;
        }
        var updateWrapper = Wrappers.<OrdOrder>lambdaUpdate()
                .set(OrdOrder::getPassengerRemark, bo.getPassengerRemark())
                .set(OrdOrder::getUpdateTime, new Date())
                .eq(OrdOrder::getId, bo.getOrderId());
        boolean b = baseMapper.update(updateWrapper) > 0;
        if (!b) {
            log.error("updateRemark，更新订单备注失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void comment(OrdOrderCommentBo bo) {
        OrdOrder ordOrder = baseMapper.selectById(bo.getOrderId());
        if (ordOrder == null) {
            log.error("订单不存在");
            return;
        }
        bo.setOrderNo(ordOrder.getOrderNo());
        ordEvaluationsService.comment(bo);

        var upd = Wrappers.<OrdOrderInfo>lambdaUpdate()
                .eq(OrdOrderInfo::getOrderId, bo.getOrderId())
                .set(OrdOrderInfo::getIsRated, YesOrNoEnum.YES.getCode());
        ordOrderInfoMapper.update(upd);
    }

    @Override
    public OrdOrderMbrPublicVo publicInfo(Long id) {
        OrdOrder ordOrder = baseMapper.selectById(id);
        if (ordOrder == null) {
            log.error("订单不存在");
            return null;
        }
        OrdOrderMbrPublicVo orderVo = BeanUtils.copyProperties(ordOrder, OrdOrderMbrPublicVo.class);
        //轨迹
        List<OrdOrderTrackLocationCacheVo> orderTrackLocations = ordCacheManager.getAllOrderTrackLocationByOrderId(ordOrder.getId());
        if (CollUtil.isNotEmpty(orderTrackLocations)) {
            orderTrackLocations = orderTrackLocations.stream().sorted(Comparator.comparing(OrdOrderTrackLocationCacheVo::getPositionTime).reversed()).toList();
            orderVo.setTrackLocationCacheVoList(orderTrackLocations);
        }
        // 获取位置信息
        List<OrdPosition> positions = positionMapper.selectList(new LambdaQueryWrapper<OrdPosition>()
                .eq(OrdPosition::getOrderId, id));
        List<OrdPositionVo> positionVos = BeanUtils.copyToList(positions, OrdPositionVo.class);
        // 遍历所有位置信息，按类型分配给起始和结束位置
        for (OrdPositionVo vo : positionVos) {
            if (Objects.equals(vo.getType(), StartEndEnum.START.getCode())) {
                orderVo.setStartPositionVo(vo);
            } else if (Objects.equals(vo.getType(), StartEndEnum.END.getCode())) {
                orderVo.setEndPositionVo(vo);
            }
        }
        // 获取司机车辆信息
        if (ordOrder.getDriverId() > 0) {
            RemoteDriverVo driverAndCarInfo = remoteDriverService.getDriverAndCar(ordOrder.getDriverId());
            if (driverAndCarInfo != null) {
                orderVo.setDriverVo(BeanUtils.copyProperties(driverAndCarInfo, MbrDriverPublicVo.class));
            }
        }
        return orderVo;
    }
}
