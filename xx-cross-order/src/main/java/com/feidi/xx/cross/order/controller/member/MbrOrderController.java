package com.feidi.xx.cross.order.controller.member;


import cn.hutool.core.util.ObjectUtil;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.PlatformEnum;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.enums.order.CreateModelEnum;
import com.feidi.xx.cross.common.enums.order.YesOrNoEnum;
import com.feidi.xx.cross.order.domain.bo.OrdOrderCommentBo;
import com.feidi.xx.cross.order.domain.bo.OrdUpdateRemarkBo;
import com.feidi.xx.cross.order.domain.bo.order.OrdOrderQueryWebBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderCancelBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderHandleBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPaymentBo;
import com.feidi.xx.cross.order.domain.handle.bo.OrdOrderPlaceBo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrListVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrPublicVo;
import com.feidi.xx.cross.order.domain.vo.OrdOrderMbrVo;
import com.feidi.xx.cross.order.domain.vo.OrdPositionVo;
import com.feidi.xx.cross.order.service.IOrdOrderMbrService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 乘客-订单
 * 前端访问路由地址为:/cross/mbr/order
 *
 * <AUTHOR>
 * @date 2024-04-26
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/order")
public class MbrOrderController extends BaseController {

    private final IOrdOrderMbrService orderMbrService;

    /**
     * 查询订单列表
     */
    @Log(title = "乘客订单")
    @Enum2TextAspect
    @GetMapping("/list")
    public TableDataInfo<OrdOrderMbrListVo> list(OrdOrderQueryWebBo bo) {
        bo.setPassengerId(LoginHelper.getUserId());
        bo.setPassengerPhone(LoginHelper.getUserPhone());
        return orderMbrService.queryPageList(bo);
    }

    /**
     * 获取订单详细信息
     *
     * @param id 主键
     */
    @Log(title = "乘客订单")
    @Enum2TextAspect
    @GetMapping("/{id}")
    public R<OrdOrderMbrVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(orderMbrService.queryById(id));
    }

    /**
     * 根据code获取订单详细信息
     *
     * @param code 主键
     */
    @Log(title = "乘客订单")
    @Enum2TextAspect
    @GetMapping("/code/{code}")
    public R<OrdOrderMbrVo> getInfo(@NotNull(message = "code不能为空")
                                    @PathVariable String code) {
        return R.ok(orderMbrService.queryByCode(code));
    }

    /**
     * 获取最近的订单(下单页面)
     */
    @Enum2TextAspect
    @GetMapping("/last")
    public R<List<OrdOrderMbrListVo>> last(OrdOrderQueryWebBo bo) {
        bo.setPassengerId(LoginHelper.getUserId());
        bo.setPassengerPhone(LoginHelper.getUserPhone());
        return R.ok(orderMbrService.queryLast(bo));
    }

    /**
     * 获取最近订单总数
     */
    @GetMapping("/total")
    public R<Integer> lastNum(OrdOrderQueryWebBo bo) {
        bo.setPassengerId(LoginHelper.getUserId());
        bo.setPassengerPhone(LoginHelper.getUserPhone());
        return R.ok(orderMbrService.lastNum(bo));
    }

    /**
     * 下单
     *
     * @param bo
     * @return
     */
    @RepeatSubmit(message = "操作频繁")
    @Log(title = "乘客订单-下单")
    @PostMapping("/place")
    public R<Long> place(@Validated @RequestBody OrdOrderPlaceBo bo) {
        bo.setPassengerId(LoginHelper.getUserId());
        bo.setPassengerPhone(LoginHelper.getUserPhone());
        if (ObjectUtil.isNotNull(bo.getDriverId())) {
            bo.setCreateModel(CreateModelEnum.PASSENGER_QRCODE.getCode());
        } else {
            bo.setCreateModel(CreateModelEnum.PASSENGER_ORDER.getCode());
        }
        return R.ok(orderMbrService.place(bo));
    }

    /**
     * 取消
     *
     * @param bo
     * @return
     */
    @RepeatSubmit(message = "操作频繁")
    @Log(title = "乘客订单-取消")
    @PutMapping("/cancel")
    public R<Boolean> cancelBo(@Validated @RequestBody OrdOrderCancelBo bo) {
        bo.setPlatformCode(PlatformEnum.SELF.getCode());
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        bo.setCancelType(CancelTypeEnum.PASSENGER.getCode());
        return R.ok(orderMbrService.cancelBo(bo));
    }

    /**
     * 行程开始
     */
    @RepeatSubmit(message = "操作频繁")
    @Log(title = "乘客订单-行程开始")
    @PutMapping("/tripStart")
    public R<Boolean> tripStart(@Validated @RequestBody OrdOrderHandleBo bo) {
        bo.setPlatformCode(PlatformEnum.SELF.getCode());
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        return R.ok(orderMbrService.tripStart(bo));
    }

    /**
     * 行程结束
     */
    @RepeatSubmit(message = "操作频繁")
    @Log(title = "乘客订单-行程结束")
    @PutMapping("/tripEnd")
    public R<Boolean> tripEnd(@Validated @RequestBody OrdOrderHandleBo bo) {
        bo.setPlatformCode(PlatformEnum.SELF.getCode());
        bo.setUserId(LoginHelper.getUserId());
        bo.setUserType(LoginHelper.getUserType().getUserType());
        return R.ok(orderMbrService.tripEnd(bo));
    }

    /**
     * 支付
     */
    @Log(title = "乘客订单-支付")
//    @RepeatSubmit(message = "操作频繁")
    @PostMapping("/payment")
    public R<Object> payment(@Validated @RequestBody OrdOrderPaymentBo bo) {
        bo.setPlatformCode(PlatformEnum.SELF.getCode());
        return R.ok(orderMbrService.payment(bo));
    }

    /**
     * 刷新支付
     */
    @Log(title = "乘客订单")
    @RepeatSubmit(message = "操作频繁")
    @PostMapping("/payment/refresh")
    public R<Void> refreshPayment(Long orderId) {
        orderMbrService.refreshPayment(orderId);
        return R.ok();
    }

    /**
     * 常用位置
     * StartEndEnum
     */
    @Log(title = "乘客订单-常用位置")
    @GetMapping("/common/position")
    public R<List<OrdPositionVo>> commonPosition() {
        return R.ok(orderMbrService.commonPosition());
    }

    /**
     * 获取司机最新位置
     */
    @GetMapping("/driver/position")
    public R<OrdOrderTrackLocationCacheVo> driverPosition(Long driverId) {

        return R.ok(orderMbrService.driverPosition(driverId));
    }

    /**
     * 修改备注（乘客）
     */
    @Log(title = "乘客订单-修改备注")
    @PostMapping("/updateRemark")
    @RepeatSubmit(message = "操作频繁")
    public R<Void> updateRemark(@Validated @RequestBody OrdUpdateRemarkBo bo) {
        orderMbrService.updateRemark(bo);
        return R.ok();
    }

    /**
     * 评价
     */
    @Log(title = "乘客订单-评价")
    @RepeatSubmit(message = "操作频繁")
    @PostMapping("/comment")
    public R<Void> comment(@Validated @RequestBody OrdOrderCommentBo bo) {
        YesOrNoEnum.getByCode(bo.getIsAnonymous());
        bo.setPassengerId(LoginHelper.getUserId());
        orderMbrService.comment(bo);
        return R.ok();
    }

    /**
     * 订单分享详情
     * 公开接口
     */
    @Log(title = "乘客订单-订单分享详情")
    @GetMapping("/public/{id}")
    @Enum2TextAspect
//    @RateLimiter(time = 60, count = 10, limitType = LimitType.IP)
    public R<OrdOrderMbrPublicVo> publicInfo(@NotNull(message = "id不能为空")
                                       @PathVariable Long id) {
        return R.ok(orderMbrService.publicInfo(id));
    }
}
