package com.feidi.xx.cross.order.domain.vo;

import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelEnumFormat;
import com.feidi.xx.cross.common.cache.order.vo.OrdOrderTrackLocationCacheVo;
import com.feidi.xx.cross.common.enums.operate.ProductCodeEnum;
import com.feidi.xx.cross.common.enums.order.CancelTypeEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrdOrderMbrPublicVo {
    /**
     * 订单id
     */
    private Long id;

    /**
     * 订单状态
     */
    @Enum2Text(enumClass = OrderStatusEnum.class, field = "showTextPsg")
    @ExcelEnumFormat(enumClass = OrderStatusEnum.class)
    private String status;
    /**
     * 取消类型
     */
    @Enum2Text(enumClass = CancelTypeEnum.class)
    @ExcelEnumFormat(enumClass = CancelTypeEnum.class)
    private String cancelType;
    /**
     * 预计出发时间
     */
    private Date earliestTime;
    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 预计最晚出发时间
     */
    private Date latestTime;

    /**
     * 发单时间
     */
    private Date createTime;

    /**
     * 乘客数量
     */
    private Integer passengerNum;

    /**
     * 产品类型
     */
    @Enum2Text(enumClass = ProductCodeEnum.class)
    @ExcelEnumFormat(enumClass = ProductCodeEnum.class)
    private String productCode;
    /**
     * 起点位置
     */
    private OrdPositionVo startPositionVo;

    /**
     * 终点位置
     */
    private OrdPositionVo endPositionVo;
    /**
     * 接单司机
     */
    private MbrDriverPublicVo driverVo;

    /**
     * 订单轨迹信息
     */
    List<OrdOrderTrackLocationCacheVo> trackLocationCacheVoList = List.of();


}
