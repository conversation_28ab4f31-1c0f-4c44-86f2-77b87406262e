<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.order.mapper.OrdOrderMapper">

    <select id="queryList" resultType="com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvDetailVo">
        select o.id, o.order_no, CONCAT_WS('**', '尾号', SUBSTR(info.phone_end, 3, 3)) as nickname, o.create_model, o.passenger_phone as phone,
        o.product_code, o.highway_type, o.status, o.third_status, o.due, o.create_time, o.pay_status, o.source, o.resell_driver_id,
        o.platform_code, o.passenger_id, o.driver_id, o.agent_id, o.order_price, o.invite_driver_id, resell_driver_price,
        o.passenger_num, o.passenger_remark, o.earliest_time, o.latest_time, o.invite_agent_id, o.receive_time,
        ps.province as start_province, ps.city as start_city, ps.district as start_district, ps.short_addr as start_addr,
        pe.province as end_province, pe.city as end_city, pe.district as end_district, pe.short_addr as end_addr,
        <if test="bo.latitude != null and bo.longitude != null">
            (
            6378137 * ACOS(
            COS(RADIANS(info.start_latitude)) *
            COS(RADIANS(#{bo.latitude})) *
            COS(RADIANS(#{bo.longitude}) - RADIANS(info.start_longitude)) +
            SIN(RADIANS(info.start_latitude)) * SIN(RADIANS(#{bo.latitude}))
            )
            ) AS distance
        </if>
        <if test="bo.latitude == null or bo.longitude == null">
            null AS distance
        </if>
        from ord_order o
        join ord_order_info info on o.id = info.order_id and info.del_flag = 0
        left join ord_position ps on o.id = ps.order_id and ps.type = 'S' and ps.del_flag = 0
        left join ord_position pe on o.id = pe.order_id and pe.type = 'E' and ps.del_flag = 0
        <where>
            o.del_flag = 0
            <choose>
                <when test="bo.isPool != null and bo.isPool == true">
                    <if test="bo.lineIds != null and bo.lineIds.size() > 0">
                        and o.line_id in
                        <foreach collection="bo.lineIds" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
                    and o.status = '1' and o.dispatch = 'N' and o.latest_time &gt; now()
                </when>
                <otherwise>
                    <if test="bo.resell != null and bo.resell != 0">
                        and o.driver_id = #{bo.driverId}
                    </if>
                </otherwise>
            </choose>
            <if test="bo.statuses != null and bo.statuses.size() > 0">
                and o.status in
                <foreach collection="bo.statuses" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.lastTime != null">
                and o.create_time &gt; #{bo.lastTime}
            </if>
            <if test="bo.inviteDriverId == null and bo.isPool != null and bo.isPool == true">
                and o.showed = 'Y'
            </if>
            <if test="bo.inviteDriverId != null">
                and o.invite_driver_id = #{bo.inviteDriverId}
            </if>
            <if test="bo.orderNo != null and bo.orderNo != ''">
                and o.order_no like CONCAT('%', #{bo.orderNo}, '%')
            </if>
            <if test="bo.productCodes != null and bo.productCodes.size() > 0">
                and o.product_code in
                <foreach collection="bo.productCodes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.payStatuses != null and bo.payStatuses.size() > 0">
                and o.pay_status in
                <foreach collection="bo.payStatuses" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.passengerNum != null and bo.passengerNum.size() > 0">
                and o.passenger_num in
                <foreach collection="bo.passengerNum" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bo.startCity != null and bo.startCity != ''">
                and o.start_city_code = #{bo.startCity}
            </if>
            <if test="bo.endCity != null and bo.endCity != ''">
                and o.end_city_code = #{bo.endCity}
            </if>
            <if test="bo.resell != null and bo.resell == 0">
                and o.resell_driver_id = #{bo.resellDriverId}
            </if>
            <if test="bo.dateTimes != null and bo.dateTimes.size() > 0">
                and
                <foreach collection="bo.dateTimes" item="item" separator="or" open="(" close=")">
                    (o.earliest_time between #{item.startTime} and #{item.endTime})
                </foreach>
            </if>
        </where>
        group by o.id, o.order_no, info.phone_end,
        o.product_code, o.highway_type, o.status, o.third_status, o.due, o.create_time, o.pay_status,
        o.platform_code, o.passenger_id, o.driver_id, o.agent_id, o.order_price,
        o.passenger_num, o.passenger_remark, o.earliest_time, o.latest_time,
        ps.province, ps.city, ps.district, ps.short_addr, pe.province, pe.city, pe.district, pe.short_addr
        <if test="bo.latitude != null and bo.longitude != null">
            , info.start_latitude, info.start_longitude
        </if>
        <if test="bo.resell != null and bo.resell == 0">
            order by o.create_time desc
        </if>

    </select>

    <select id="queryDetail" resultType="com.feidi.xx.cross.order.domain.vo.driver.OrdOrderDrvDetailVo">
        select o.id, o.order_no, CONCAT_WS('**', '尾号', SUBSTR(info.phone_end, 3, 3)) as nickname, o.invite_driver_id, o.resell_driver_id,
        o.product_code, o.highway_type, o.status, o.third_status, o.due, o.create_time, platform_no, o.invite_agent_id,
        o.platform_code, o.passenger_id, o.driver_id, o.agent_id, o.order_price, o.rebate_status, o.create_model,
        o.highway_type, o.pay_status, o.cancel_time, o.arrival_time, o.passenger_phone, o.source, resell_driver_price,
        o.passenger_num, o.passenger_remark, o.earliest_time, o.latest_time, o.due, info.virtual_phone, info.virtual_dispatch,
        <if test="bo.latitude != null and bo.longitude != null">
            (
                6378137 * ACOS(
                COS(RADIANS(info.start_latitude)) *
                COS(RADIANS(#{bo.latitude})) *
                COS(RADIANS(#{bo.longitude}) - RADIANS(info.start_longitude)) +
                SIN(RADIANS(info.start_latitude)) * SIN(RADIANS(#{bo.latitude}))
            )
            ) AS start_distance, (
                6378137 * ACOS(
                COS(RADIANS(info.end_latitude)) *
                COS(RADIANS(#{bo.latitude})) *
                COS(RADIANS(#{bo.longitude}) - RADIANS(info.end_longitude)) +
                SIN(RADIANS(info.end_latitude)) * SIN(RADIANS(#{bo.latitude}))
            )
            ) AS end_distance
        </if>
        <if test="bo.latitude == null or bo.longitude == null">
            null AS start_distance, null as end_distance
        </if>
        from ord_order o
        join ord_order_info info on o.id = info.order_id and info.del_flag = 0
        <where>
            o.del_flag = 0 and o.id = #{bo.id}
        </where>
    </select>

    <select id="queryCanDeleteOrders" resultType="java.lang.Long">
        select id
        from ord_order
        where dispatch = 'N'
          and status = '0'
          and driver_id = 0
          and create_time <![CDATA[<=]]> #{deleteTime}
    </select>
    <select id="queryOrderCountByDriverIdsAndStatuses" resultType="com.feidi.xx.cross.common.domain.QueryCountBo">
        select driver_id as groupId, count(1) as num
        from ord_order
        where driver_id in
        <foreach collection="driverIds" item="driverId" open="(" separator="," close=")">
            #{driverId}
        </foreach>
        <if test="orderStatuses != null and orderStatuses.size() > 0">
            and status in
            <foreach collection="orderStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        group by driver_id
    </select>

    <delete id="deleteByOrderIds">
        delete from ord_order
        where id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>
