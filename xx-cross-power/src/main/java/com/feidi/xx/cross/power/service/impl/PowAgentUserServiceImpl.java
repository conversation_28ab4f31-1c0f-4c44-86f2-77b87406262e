package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.feidi.xx.common.core.enums.UserStatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.manager.PowCacheManager;
import com.feidi.xx.cross.common.constant.power.PowerConstants;
import com.feidi.xx.cross.common.enums.power.AgentRoleType;
import com.feidi.xx.cross.order.api.RemoteAuthService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import com.feidi.xx.cross.power.domain.PowAgent;
import com.feidi.xx.cross.power.domain.pojo.bo.PasswordForm;
import com.feidi.xx.cross.power.domain.vo.PowAgentVo;
import com.feidi.xx.cross.power.mapper.PowAgentMapper;
import com.feidi.xx.cross.power.service.IPowAgentService;
import com.feidi.xx.resource.api.RemoteFileService;
import com.feidi.xx.resource.api.domain.RemoteFile;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.secure.BCrypt;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import com.feidi.xx.cross.power.domain.bo.PowAgentUserBo;
import com.feidi.xx.cross.power.domain.vo.PowAgentUserVo;
import com.feidi.xx.cross.power.domain.PowAgentUser;
import com.feidi.xx.cross.power.mapper.PowAgentUserMapper;
import com.feidi.xx.cross.power.service.IPowAgentUserService;
import com.feidi.xx.common.core.utils.StringUtils;
import org.springframework.util.Assert;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 代理商用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class PowAgentUserServiceImpl implements IPowAgentUserService {

    private final PowAgentUserMapper baseMapper;

    private final PowAgentMapper  agentMapper;

    private final PowCacheManager powCacheManager;

    @DubboReference
    private final RemoteFileService remoteFileService;

    @DubboReference
    private final RemoteConfigService remoteConfigService;

    @DubboReference
    private final RemoteAuthService remoteAuthService;


    /**
     * 查询代理商用户
     *
     * @param id 主键
     * @return 代理商用户
     */
    @Override
    public PowAgentUserVo queryById(Long id) {
        PowAgentUserVo powAgentUserVo = baseMapper.selectVoById(id);
        RemoteAgentVo remoteAgentVo = powCacheManager.getAgentInfoById(powAgentUserVo.getAgentId());
        powAgentUserVo.setCompanyName(remoteAgentVo.getCompanyName());
        return powAgentUserVo;
    }

    /**
     * 根据手机号查询代理商
     */
    @Override
    public PowAgentUser queryByPhone(String phone) {
        LambdaQueryWrapper<PowAgentUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(PowAgentUser::getPhone, phone);
        return baseMapper.selectOne(lqw);
    }

    /**
     * 分页查询代理商用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代理商用户分页列表
     */
    @Override
    public TableDataInfo<PowAgentUserVo> queryPageList(PowAgentUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowAgentUser> lqw = buildQueryWrapper(bo);
        IPowAgentService powAgentService = SpringUtils.getBean(IPowAgentService.class);
        Page<PowAgentUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().forEach(item -> {
            PowAgentVo powAgentVo = powAgentService.queryById(item.getAgentId());
            item.setCompanyName(powAgentVo.getCompanyName());
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代理商用户列表
     *
     * @param bo 查询条件
     * @return 代理商用户列表
     */
    @Override
    public List<PowAgentUserVo> queryList(PowAgentUserBo bo) {
        LambdaQueryWrapper<PowAgentUser> lqw = buildQueryWrapper(bo);
        List<PowAgentUserVo> resultList = baseMapper.selectVoList(lqw);
        fillInfo(resultList);
        return resultList;
    }

    private void fillInfo(List<PowAgentUserVo> resultList) {
        List<Long> agentIds = resultList.stream().map(PowAgentUserVo::getAgentId).toList();
        Map<Long, PowAgent> agentMap = agentMapper.selectBatchIds(agentIds).stream().collect(Collectors.toMap(PowAgent::getId, Function.identity()));
        resultList.forEach(vo -> {
            PowAgent agent = agentMap.get(vo.getAgentId());
            if (agent != null) {
                vo.setParentId(agent.getParentId());
                PowAgent parentAgent = agentMap.get(agent.getParentId());
                vo.setParentAgentName(parentAgent != null ? parentAgent.getCompanyName() : null);
                vo.setCompanyName(agent.getCompanyName());
                vo.setTaxNo(agent.getTaxNo());
                vo.setCityCode(agent.getCityCode());
            }
        });
    }

    private LambdaQueryWrapper<PowAgentUser> buildQueryWrapper(PowAgentUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PowAgentUser> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(bo.getName())) {
            lqw.nested(l -> {
                l.like(PowAgentUser::getName, bo.getName()).or()
                        .like(PowAgentUser::getPhone, bo.getName()).or()
                        .like(PowAgentUser::getEmail, bo.getName()).or()
                        .like(PowAgentUser::getInviteCode, bo.getName()).or();
            });
        }

        lqw.eq(bo.getAgentId() != null, PowAgentUser::getAgentId, bo.getAgentId());
        //lqw.like(StringUtils.isNotBlank(bo.getName()), PowAgentUser::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getRole()), PowAgentUser::getRole, bo.getRole());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), PowAgentUser::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), PowAgentUser::getPassword, bo.getPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteCode()), PowAgentUser::getInviteCode, bo.getInviteCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PowAgentUser::getStatus, bo.getStatus());
        lqw.orderByDesc(PowAgentUser::getCreateTime);
        return lqw;
    }

    /**
     * 新增代理商用户
     *
     * @param bo 代理商用户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowAgentUserBo bo) {
        bo.setStatus(UserStatusEnum.OK.getCode());
        bo.setInviteCode(makeCode());
        PowAgentUser add = MapstructUtils.convert(bo, PowAgentUser.class);
        validEntityBeforeSave(add);

        // 处理密码
        add.setPassword(BCrypt.hashpw(bo.getPhone()));

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }



    /**
     * 生成邀请码
     *
     * @return
     */
    private String makeCode() {
        String code = RandomUtil.randomString(8);
        //todo 校验邀请码
        Boolean exists = this.exists(code);
        if (exists) {
            makeCode();
        }
        return code;
    }

    /**
     * 修改代理商用户
     *
     * @param bo 代理商用户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowAgentUserBo bo) {
        PowAgentUser update = MapstructUtils.convert(bo, PowAgentUser.class);
        validEntityBeforeSave(update);
        update.setPassword(BCrypt.hashpw(bo.getPhone()));
        PowAgentUser powAgentUser = baseMapper.selectById(update.getId());
        boolean ret = baseMapper.updateById(update) > 0;
        // 禁用或者更换代理商需要下线
        if (ret && !Objects.equals(bo.getStatus(), UserStatusEnum.OK.getCode()) || !Objects.equals(bo.getAgentId(), powAgentUser.getAgentId())) {
            String loginId = LoginHelper.getLoginId(UserTypeEnum.AGENT_USER.getUserType(), bo.getId());
            ThreadUtil.execAsync(() -> remoteAuthService.logout(loginId));
        }
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowAgentUser entity) {
        Assert.isTrue(StrUtil.isAllNotBlank(entity.getPhone()), "手机号不能为空");
        // id 是否为空
        if (ObjectUtil.isNull(entity.getId())) {
            // 新增的时候判断是否已有
            boolean exists = baseMapper.exists(new LambdaQueryWrapper<PowAgentUser>().eq(PowAgentUser::getPhone, entity.getPhone()));
            Assert.isTrue(!exists, "该手机号已注册代理商");
        }else {
            PowAgentUser powAgentUser = baseMapper.selectById(entity.getId());
            if (!powAgentUser.getPhone().equals(entity.getPhone())){
                boolean exists = baseMapper.exists(new LambdaQueryWrapper<PowAgentUser>().eq(PowAgentUser::getPhone, entity.getPhone()));
                Assert.isTrue(!exists, "该手机号已注册代理商");
            }
        }
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代理商用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            List<PowAgentUser> powAgentUsers = baseMapper.selectBatchIds(ids);
            List<Long> agentIds = powAgentUsers.stream().map(PowAgentUser::getAgentId).toList();

            Map<Long, Long> agentUserCountMap = baseMapper.selectList(new LambdaQueryWrapper<PowAgentUser>()
                            .in(PowAgentUser::getAgentId, agentIds))
                    .stream()
                    .collect(Collectors.groupingBy(PowAgentUser::getAgentId, Collectors.counting()));

            for (Long agentId : agentIds) {
                if (agentUserCountMap.getOrDefault(agentId, 0L) == 1) {
                    throw new ServiceException("代理商唯一用户不能被删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 校验邀请码是否存在
     *
     * @param code 邀请码
     */
    public Boolean exists(String code) {
        LambdaQueryWrapper<PowAgentUser> pqw = new LambdaQueryWrapper<>();
        pqw.eq(PowAgentUser::getInviteCode, code);
        return baseMapper.exists(pqw);
    }

    /**
     * 根据代理商id查询管理员信息
     *
     * @param id 代理商id
     * @return 管理员信息
     */
    @Override
    public PowAgentUser queryByAgentId(Long id) {
        LambdaQueryWrapper<PowAgentUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PowAgentUser::getAgentId, id)
                .eq(PowAgentUser::getRole, AgentRoleType.ADMIN.getCode())
                .orderByDesc(PowAgentUser::getId)
                .last("limit 1");
        PowAgentUser powAgentUser = baseMapper.selectOne(wrapper);
        return powAgentUser;
    }

    @Override
    public Boolean resetPwd(PasswordForm bo) {
        PowAgentUser powAgentUser = baseMapper.selectById(LoginHelper.getUserId());
        String password = powAgentUser.getPassword();
        if (!BCrypt.checkpw(bo.getOldPassword(), password)) {
            throw new ServiceException("修改密码失败，旧密码错误");
        }
        if (BCrypt.checkpw(bo.getNewPassword(), password)) {
            throw new ServiceException("新密码不能与旧密码相同");
        }
        powAgentUser.setPassword(BCrypt.hashpw(bo.getNewPassword()));
        return baseMapper.updateById(powAgentUser) > 0;
    }


    /**
     * 生成验证码
     *
     * @return
     */
    @Override
    public PowAgentUserVo generateInviteCode() {
       Long userId = LoginHelper.getUserId();
        PowAgentUser agentUser = baseMapper.selectById(userId);
       Assert.notNull(agentUser, "代理商账号不存在");
       IPowAgentService powAgentService = SpringUtils.getBean(IPowAgentService.class);
        PowAgentVo powAgentVo = powAgentService.queryById(agentUser.getId());
        String inviteCode = agentUser.getInviteCode();
       if (StrUtil.isBlank(inviteCode)) {
           agentUser.setInviteCode(makeCode());
       }
       UserTypeEnum userType = LoginHelper.getUserType();
       //邀请链接
       String inviteUrl = remoteConfigService.selectValueByKey(PowerConstants.AGENT_INVITE_URL)+inviteCode+"&userType="+userType.name();
       //邀请二维码
       BufferedImage image = QrCodeUtil.generate(inviteUrl,200,200);
       // 创建 ByteArrayOutputStream 用来保存图像的字节流
       ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
       try {
           ImageIO.write(image, "jpg", byteArrayOutputStream);
       } catch (IOException e) {
           throw new RuntimeException(e);
       }
       RemoteFile remoteFile = remoteFileService.upload(powAgentVo.getCompanyName(), powAgentVo.getCompanyName()+ ".jpg", image.toString(), byteArrayOutputStream.toByteArray());
       RemoteFile remoteFile1 = remoteFileService.selectById(remoteFile.getOssId());
       PowAgentUserVo agentUserVo = new PowAgentUserVo();
       agentUserVo.setInviteUrl(inviteUrl);
       agentUserVo.setInviteImage(remoteFile1.getUrl());
        return agentUserVo;
    }
}
