package com.feidi.xx.cross.power.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.IsYesEnum;
import com.feidi.xx.common.core.enums.SmsUseEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.SpringUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.core.utils.ValidatorUtils;
import com.feidi.xx.common.core.utils.xx.ArithUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.common.enums.power.RejectCertTypeEnum;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.message.api.RemoteImService;
import com.feidi.xx.cross.power.domain.*;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.PowRejectReason;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.domain.vo.PowGroupVo;
import com.feidi.xx.cross.power.mapper.*;
import com.feidi.xx.cross.power.service.IPowCarService;
import com.feidi.xx.cross.power.service.IPowDriverApplyService;
import com.feidi.xx.cross.power.service.IPowDriverReview;
import com.feidi.xx.cross.power.service.IPowGroupService;
import com.feidi.xx.cross.power.validate.ReviewGroup;
import com.feidi.xx.cross.power.validate.ReviewPassGroup;
import com.feidi.xx.cross.power.validate.ReviewRejectGroup;
import com.feidi.xx.push.common.enums.PushTypeEnum;
import com.feidi.xx.push.mq.PushEvent;
import com.feidi.xx.push.mq.PushMsgProducer;
import com.feidi.xx.resource.api.RemoteSmsService;
import com.feidi.xx.system.api.RemoteDictService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 司机审核Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PowDriverReviewImpl implements IPowDriverReview {

    private final PowDriverMapper baseMapper;

    private final PowAgentMapper agentMapper;

    private final PowGroupMapper groupMapper;

    private final PowAuditRecordMapper auditRecordMapper;

    private final PowDriverRateMapper rateMapper;

    private final IPowDriverApplyService driverApplyService;

    private final IPowGroupService groupService;

    private final IPowCarService carService;

    private final ScheduledExecutorService scheduledExecutorService;


    @DubboReference
    private final RemoteSmsService remoteSmsService;

    @DubboReference
    private final RemoteDictService remoteDictService;

    @DubboReference
    private final RemoteImService remoteImService;

    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;

    private final PowAgentLineMapper agentLineMapper;

    private final PowDriverLineMapper driverLineMapper;


    /**
     * 查询司机审核列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<PowDriverVo> queryPageList(PowDriverBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriver> lqw = buildQueryWrapper(bo);
        Page<PowDriver> result = baseMapper.selectPage(pageQuery.build(), lqw);
        List<PowDriverVo> records = BeanUtils.copyToList(result.getRecords(), PowDriverVo.class);
        //车牌号
        Map<Long, String> carMap = getCarMap(records.stream().map(PowDriverVo::getId).toList());
        if (CollUtil.isNotEmpty(records)) {
            Map<Long, String> agentMap = getAgentMap();
            Map<Long, String> recordMap = getRecordMap(result.getRecords());
            records.forEach(item -> {
                item.setCarNumber(carMap.get(item.getId()));
                item.setAgentName(agentMap.get(item.getAgentId()));
                item.setAuditUser(recordMap.get(item.getId()));
            });
        }
        return new TableDataInfo<>(records, result.getTotal());
    }

    @Override
    public List<PowDriverVo> queryList(PowDriverBo bo) {
        LambdaQueryWrapper<PowDriver> lqw = buildQueryWrapper(bo);
        List<PowDriver> powDriverVoList = baseMapper.selectList(lqw);
        List<PowDriverVo> driverVoList = null;
        if (CollUtil.isNotEmpty(powDriverVoList)) {
            Map<Long, String> agentMap = getAgentMap();
            Map<Long, String> recordMap = getRecordMap(powDriverVoList);
            driverVoList = BeanUtils.copyToList(powDriverVoList, PowDriverVo.class);
            driverVoList.forEach(item -> {
                item.setAgentName(agentMap.get(item.getAgentId()));
                item.setAuditUser(recordMap.get(item.getId()));
            });
        }
        return driverVoList;
    }

    /**
     * 获取司机审核详情
     *
     * @param id
     * @return
     */
    @Override
    public PowDriverVo reviewDetail(Long id) {
        PowDriver driver = baseMapper.selectById(id);
        if (driver == null) {
            throw new ServiceException("司机信息不存在");
        }
        if (!driver.getAuditStatus().equals(DrvAuditStatusEnum.ING.getCode())) {
            throw new ServiceException("司机信息不在审核状态");
        }
        PowDriverVo driverVo = driverApplyService.getStorage(id);
        //代理商
        if (driver.getAgentId() != null && driver.getAgentId() != 0) {
            PowAgent powAgent = agentMapper.selectById(driver.getAgentId());
            if (powAgent != null) {
                driverVo.setAgentName(powAgent.getCompanyName());
                driverVo.setAgentId(driver.getAgentId());
            }
        }
        //司机组
        if (driver.getGroupId() != null && driver.getGroupId() != 0) {
            PowGroup powGroup = groupMapper.selectById(driver.getGroupId());
            if (powGroup != null) {
                driverVo.getDriverVo().setGroupName(powGroup.getName());
                driverVo.setGroupId(driver.getGroupId());
                driverVo.setRate(powGroup.getRate());
            }
        }
        //TODO 邀请信息
        return driverVo;
    }

    /**
     * 司机审核
     *
     * @param bo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean review(PowDriverBo bo) {
        // 审核前校验
        validBeforeReview(bo);
        PowDriver powDriver = baseMapper.selectOne(new LambdaQueryWrapper<PowDriver>().eq(PowDriver::getId, bo.getId()));
        // 添加审核记录
        PowAuditRecord auditRecord = createAuditRecord(bo);
        auditRecordMapper.insert(auditRecord);
        if(ObjectUtil.isNotEmpty(bo.getIdCardBo().getReasons())||ObjectUtil.isNotEmpty(bo.getDrivingBo().getReasons())||ObjectUtil.isNotEmpty(bo.getCarBo().getReasons())
        ||ObjectUtil.isNotEmpty(bo.getAgreementBo().getReasons())){
            //驳回只更改司机状态,添加审核记录
            baseMapper.update(new LambdaUpdateWrapper<PowDriver>().eq(PowDriver::getId, bo.getId())
                    .set(PowDriver::getAuditStatus, DrvAuditStatusEnum.REJECT.getCode())
                    .set(PowDriver::getAuditTime, DateUtil.date()));
            sendMessageAsync(powDriver.getPhone(), DrvAuditStatusEnum.REJECT);
            // 驳回消息
            scheduledExecutorService.submit(() -> {
                pushToDriver(DrvAuditStatusEnum.REJECT, powDriver);
            });
            return true;
        }

        PowDriver driver = validateBeforeReview(bo);
        // 短信 审核结果
        DrvAuditStatusEnum auditStatus = DrvAuditStatusEnum.SUCCESS;
        //更新证件信息
        updateDriverInfo(bo);
        // 修改司机信息
        if (Objects.equals(auditRecord.getStatus(), DrvAuditStatusEnum.SUCCESS.getCode())) {
            // 父代理商id
            // fixme 注册的时候A的父代理商可能在审核的时候变了，所以不依赖注册时候的父代理商信息，以最新的为主
            if (ArithUtils.isNotNull(bo.getAgentId())) {
                PowAgent agent = agentMapper.selectById(bo.getAgentId());
                driver.setParentId(agent.getParentId());
                if(bo.getAssignRoute()){
                    insertDriverLines(bo, driver);
                }
            }
            // 认证信息
            driver.setIdentity(DriverIdentityEnum.CROSS_DRV.getCode());
            driver.setReceive(IsYesEnum.YES.getCode());
            driver.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
            driver.setName(bo.getName());
            //佣金比例
            PowDriverRate powDriverRate = new PowDriverRate();
            powDriverRate.setRate(bo.getRate());
            powDriverRate.setPlatformCode(PlatformCodeEnum.TY.getCode());
            powDriverRate.setDriverId(bo.getId());
            rateMapper.insert(powDriverRate);
            // 保存之前补充一些属性在里面
            validEntityBeforeSave(driver);
        }
        boolean flag = baseMapper.updateById(driver) > 0;
        if (flag) {
            if (Objects.equals(auditRecord.getStatus(), DrvAuditStatusEnum.SUCCESS.getCode())) {
                // 审核成功后，删除缓存数据
                deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_IDENTIFY_CACHE_KEY.create(driver.getId()));
                deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_DRIVING_CACHE_KEY.create(driver.getId()));
                deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_CAR_CACHE_KEY.create(driver.getId()));
                deleteTemporaryData(PowCacheKeyEnum.POW_DRIVER_APPLY_AGREEMENT_CACHE_KEY.create(driver.getId()));

                // 删除司机身份信息缓存
                String cacheKey = PowCacheKeyEnum.POW_DRIVER_INFO_CACHE_KEY.create(driver.getId());
                RedisUtils.deleteObject(cacheKey);

                // 创建钱包
                remoteDrvWalletService.makeDrvWallet(driver.getId());
            }
            // 异步发送短信
            try {
                sendMessageAsync(powDriver.getPhone(), DrvAuditStatusEnum.SUCCESS);
                // 推送消息
                scheduledExecutorService.submit(() -> {
                    pushToDriver(DrvAuditStatusEnum.SUCCESS, driver);
                });
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                log.error("短信-审核结果-发送失败；手机号：【{}】-结果：【{}】-错误原因：【{}】", bo.getPhone(), auditStatus.getInfo(), e.getMessage());
            }
        }
        return flag;
    }

    /**
     * 根据代理商分配司机路线
     * @param bo
     * @param driver
     */
    private void insertDriverLines(PowDriverBo bo, PowDriver driver) {
        //获取代理商路线
        List<PowAgentLine> powAgentLines = agentLineMapper.listByAgentId(bo.getAgentId());
        if(CollUtil.isNotEmpty(powAgentLines)){
            List<PowDriverLine> powDriverLineList = new ArrayList<>();
            for (PowAgentLine powAgentLine : powAgentLines) {
                //给司机分配路径
                PowDriverLine powDriverLine = new PowDriverLine();
                powDriverLine.setDriverId(driver.getId());
                powDriverLine.setLineId(powAgentLine.getLineId());
                powDriverLine.setAgentId(bo.getAgentId());
                powDriverLineList.add(powDriverLine);
            }
            driverLineMapper.insertBatch(powDriverLineList);
        }
    }

    /**
     * 审计审核前校验
     */
    public void validBeforeReview(PowDriverBo bo) {
        if (bo.getGroupId() != null && bo.getGroupId() > 0) {
            PowGroup group = groupMapper.selectById(bo.getGroupId());
            if (group != null && !Objects.equals(group.getAgentId(), bo.getAgentId())) {
                throw new ServiceException("所属代理商与所属司机组不匹配");
            }
        }
    }

    private void pushToDriver(DrvAuditStatusEnum auditStatus, PowDriver driver) {
        PushTypeEnum pushType = DrvAuditStatusEnum.SUCCESS.equals(auditStatus) ?
                PushTypeEnum.DRIVER_APPLICATION_APPROVED
                : PushTypeEnum.DRIVER_APPLICATION_REJECTED;
        PushEvent event = new PushEvent(pushType);

        event.setTenantId(driver.getTenantId());

        // 接收人
        event.getReceiverParam().setUserType(UserTypeEnum.DRIVER_USER.getUserType());
        event.getReceiverParam().setUserId(driver.getId());

        List<String> cid = remoteImService.getCid(UserTypeEnum.DRIVER_USER.getUserType(), driver.getId());
        event.setCids(cid);
        PushMsgProducer.sendMessage(event);
    }

    /**
     * 异步发送短信
     * @param phone
     * @param auditStatus
     */
    private void sendMessageAsync(String phone, DrvAuditStatusEnum auditStatus) {
        SmsUseEnum smsUseTemplate = SmsUseEnum.DRIVER_REVIEW;
        String smsParam = DrvAuditStatusEnum.SUCCESS.equals(auditStatus) ? "通过" : "驳回";
        LinkedHashMap<String, String> smsParamMap = new LinkedHashMap<>();
        smsParamMap.put(smsUseTemplate.getVariable(), smsParam);
        remoteSmsService.sendMessageAsync(phone, smsUseTemplate.getTemplateId(), smsParamMap);
    }

    private LambdaQueryWrapper<PowDriver> buildQueryWrapper(PowDriverBo bo) {
        LambdaQueryWrapper<PowDriver> lqw = Wrappers.lambdaQuery();

        lqw.nested(ObjectUtil.isNotNull(bo.getUnionId()),
                l -> l.like(PowDriver::getId, bo.getUnionId()).or()
                        .like(PowDriver::getCardNo, bo.getUnionId()).or()
                        .like(PowDriver::getName, bo.getUnionId()).or()
                        .like(PowDriver::getPhone, bo.getUnionId())
        );

        lqw.eq(ObjectUtil.isNotEmpty(bo.getCityCode()), PowDriver::getCityCode, bo.getCityCode());
        lqw.eq(ObjectUtil.isNotEmpty(bo.getAgentId()), PowDriver::getAgentId, bo.getAgentId());
        lqw.eq(ObjectUtil.isNotEmpty(bo.getType()), PowDriver::getType, bo.getType());
        lqw.eq(ObjectUtils.isNotNull(bo.getAuditStatus()), PowDriver::getAuditStatus, bo.getAuditStatus());
        lqw.eq(ObjectUtils.isNotNull(bo.getSource()), PowDriver::getSource, bo.getSource());
        lqw.ge(ObjectUtil.isNotEmpty(bo.getStartTime()), PowDriver::getSubmitTime, bo.getStartTime());
        lqw.le(ObjectUtil.isNotEmpty(bo.getEndTime()), PowDriver::getSubmitTime, bo.getEndTime());
        lqw.orderByAsc(PowDriver::getSubmitTime);
        return lqw;
    }
    private PowDriver validateBeforeReview(PowDriverBo bo) {
        PowDriver driver = baseMapper.selectById(bo.getId());
        String auditStatus = driver.getAuditStatus();
        if (!(auditStatus.equals(DrvAuditStatusEnum.ING.getCode()) || auditStatus.equals(DrvAuditStatusEnum.REJECT.getCode()))) {
            throw new ServiceException("审核状态错误");
        } else {
            if (auditStatus.equals(DrvAuditStatusEnum.SUCCESS.getCode())) {
                ValidatorUtils.validate(bo, ReviewPassGroup.class);
            } else {
                ValidatorUtils.validate(bo, ReviewRejectGroup.class);
            }
        }

        //校验
        ValidatorUtils.validate(bo.getIdCardBo(), ReviewGroup.class);
        ValidatorUtils.validate(bo.getDrivingBo(), ReviewGroup.class);
        ValidatorUtils.validate(bo.getCarBo() , ReviewGroup.class);
        Assert.isTrue(ObjectUtil.isNotNull(bo.getAgentId()), "代理商不能为空");
        Assert.isTrue(ObjectUtil.isNotNull(bo.getGroupId()), "司机组不能为空");
        Assert.isTrue(driver.getIdentity().equals(DriverIdentityEnum.NO.getCode()), "该用户已是司机");
        PowGroupVo powGroupVo = groupService.queryById(bo.getGroupId());
        // 更新新属性到旧对象
        driver.setAgentId(bo.getAgentId());
        driver.setGroupId(bo.getGroupId());
        driver.setType(powGroupVo.getType());
        driver.setAuditTime(DateUtil.date());
        // 订单转卖服务费比例
        driver.setResellServiceRate(bo.getResellServiceRate());
        return driver;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowDriver entity) {
        if (StringUtils.isEmpty(entity.getBirthday()) && StringUtils.isNotBlank(entity.getCardNo())) {
            String birthByIdCard = IdcardUtil.getBirthByIdCard(entity.getCardNo());
            entity.setBirthday(birthByIdCard);
        }
        if (entity.getId() != null) {
            PowDriver phone = baseMapper.getByPhone(entity.getPhone());
            Assert.isTrue(phone == null || phone.getId().equals(entity.getId()), "该手机号已注册");
        }
    }

    /**
     * 获取车牌号
     */
    private Map<Long, String> getCarMap(List<Long> ids) {
        Map<Long, String> carMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ids)) {
            List<PowCarVo> cars = carService.queryByDriverIds(ids);
            for (PowCarVo car : cars) {
                carMap.put(car.getDriverId(), car.getCarNumber());
            }
        }
        return carMap;
    }

    private Map<Long, String> getAgentMap() {
        List<PowAgent> powAgents = agentMapper.selectList();
        Map<Long, String> agentMap = powAgents.stream().collect(Collectors.toMap(PowAgent::getId, PowAgent::getCompanyName));
        return agentMap;
    }

    private Map<Long, String> getRecordMap(List<PowDriver> records) {
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> ids = records.stream().map(PowDriver::getId).filter(Objects::nonNull).collect(Collectors.toSet());

            List<PowAuditRecord> entities = auditRecordMapper.selectList(
                    new LambdaQueryWrapper<PowAuditRecord>().in(PowAuditRecord::getDriverId, ids)
                            .orderByDesc(PowAuditRecord::getCreateTime));

            if (CollUtil.isNotEmpty(entities)) {
                Map<Long, String> entityMap = entities.stream().collect(Collectors.toMap(
                        PowAuditRecord::getDriverId,
                        PowAuditRecord::getAuditUser,
                        (existing, replacement) -> existing
                ));
                return entityMap;
            }
        }
        return new HashMap<>();
    }
    public void updateDriverInfo(PowDriverBo bo) {
        PowDriverApplyServiceImpl driverApply = SpringUtils.getBean(PowDriverApplyServiceImpl.class);
        bo.getIdCardBo().setDriverId(bo.getId());
        bo.getDrivingBo().setDriverId(bo.getId());
        bo.getCarBo().setDriverId(bo.getId());
        driverApply.uploadIdCard(bo.getIdCardBo());
        driverApply.uploadDriving(bo.getDrivingBo());
        driverApply.uploadCar(bo.getCarBo());
        bo.getAgreementBo().setDriverId(bo.getId());
        driverApply.uploadAgreement(bo.getAgreementBo());
    }

    private PowAuditRecord createAuditRecord(PowDriverBo bo) {
        PowAuditRecord auditRecord = new PowAuditRecord();
        if (Stream.of(bo.getIdCardBo().getReasons(), bo.getDrivingBo().getReasons(), bo.getCarBo().getReasons()
                ,bo.getAgreementBo().getReasons())
                .anyMatch(reasons -> reasons != null && !reasons.isEmpty())) {
            auditRecord.setStatus(DrvAuditStatusEnum.REJECT.getCode());
            String jsonStr = buildRejectReason(bo);
            auditRecord.setReasons(jsonStr);
        } else {
            auditRecord.setStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        }

        auditRecord.setDriverId(bo.getId());
        auditRecord.setAuditUser(LoginHelper.getUsername());
        auditRecord.setCreateTime(new Date());
        return auditRecord;
    }

    private static String buildRejectReason(PowDriverBo bo) {
        // 构造驳回原因列表
        List<PowRejectReason> PowRejectReason = new ArrayList<>();
        if (bo.getIdCardBo().getReasons() != null){
            PowRejectReason rejectReason = new PowRejectReason();
            rejectReason.setType(RejectCertTypeEnum.ID_CARD.getCode());
            rejectReason.setReason(bo.getIdCardBo().getReasons());
            PowRejectReason.add(rejectReason);
        }
        if (bo.getDrivingBo().getReasons() != null){
            PowRejectReason rejectReason = new PowRejectReason();
            rejectReason.setType(RejectCertTypeEnum.DRIVING.getCode());
            rejectReason.setReason(bo.getDrivingBo().getReasons());
            PowRejectReason.add(rejectReason);
        }
        if (bo.getCarBo().getReasons() != null){
            PowRejectReason rejectReason = new PowRejectReason();
            rejectReason.setType(RejectCertTypeEnum.CAR.getCode());
            rejectReason.setReason(bo.getCarBo().getReasons());
            PowRejectReason.add(rejectReason);
        }

        if (bo.getAgreementBo().getReasons() != null){
            PowRejectReason rejectReason = new PowRejectReason();
            rejectReason.setType(RejectCertTypeEnum.AGREEMENT.getCode());
            rejectReason.setReason(bo.getAgreementBo().getReasons());
            PowRejectReason.add(rejectReason);
        }
        return JSONUtil.toJsonStr(PowRejectReason);
    }

    /**
     * 清除缓存
     */
    public void deleteTemporaryData(String key) {
        if (RedisUtils.hasKey(key)) {
            RedisUtils.deleteObject(key);
            log.info("{}删除成功", key);
        }
    }

}
