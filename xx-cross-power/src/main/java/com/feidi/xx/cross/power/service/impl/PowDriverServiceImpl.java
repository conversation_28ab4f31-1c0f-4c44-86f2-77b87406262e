package com.feidi.xx.cross.power.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.*;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.cache.system.DistrictCacheVo;
import com.feidi.xx.common.core.constant.SmsConstant;
import com.feidi.xx.common.core.enums.*;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.*;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.map.model.tencent.regeo.TxMapReGeocode;
import com.feidi.xx.common.map.utils.TxMapUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.sms.util.SmsUtil;
import com.feidi.xx.common.tenant.helper.TenantHelper;
import com.feidi.xx.cross.common.cache.operate.manager.OprCacheManager;
import com.feidi.xx.cross.common.cache.power.enums.PowCacheKeyEnum;
import com.feidi.xx.cross.common.constant.finance.FinanceConstants;
import com.feidi.xx.cross.common.constant.power.PowerConstants;
import com.feidi.xx.cross.common.enums.finance.AccountTypeEnum;
import com.feidi.xx.cross.common.enums.finance.PwdVerifyEnum;
import com.feidi.xx.cross.common.enums.order.OrderStatusTabEnum;
import com.feidi.xx.cross.common.enums.order.PlatformCodeEnum;
import com.feidi.xx.cross.common.enums.power.DriverIdentityEnum;
import com.feidi.xx.cross.common.enums.power.DriverTypeEnum;
import com.feidi.xx.cross.common.enums.power.DrvAuditStatusEnum;
import com.feidi.xx.cross.common.enums.power.DrvCertTypeEnum;
import com.feidi.xx.cross.common.utils.ExceptionUtil;
import com.feidi.xx.cross.finance.api.RemoteDrvWalletService;
import com.feidi.xx.cross.finance.api.domain.vo.RemoteWalletVo;
import com.feidi.xx.cross.operate.api.RemoteCityService;
import com.feidi.xx.cross.operate.api.domain.city.vo.RemoteCityVo;
import com.feidi.xx.cross.order.api.RemoteAuthService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.RemoteRobService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDrvLoginVo;
import com.feidi.xx.cross.power.domain.*;
import com.feidi.xx.cross.power.domain.bo.PowDriverBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverCapitalPwdForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverImportBo;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverPhoneForm;
import com.feidi.xx.cross.power.domain.pojo.bo.PowDriverVoucherBo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverAgreementVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDriverCarVo;
import com.feidi.xx.cross.power.domain.pojo.vo.PowDrvDrivingVo;
import com.feidi.xx.cross.power.domain.vo.PowCarVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverAccountVo;
import com.feidi.xx.cross.power.domain.vo.PowDriverVo;
import com.feidi.xx.cross.power.mapper.*;
import com.feidi.xx.cross.power.service.*;
import com.feidi.xx.resource.api.RemoteFileService;
import com.feidi.xx.resource.api.domain.RemoteFile;
import com.feidi.xx.system.api.RemoteConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PowDriverServiceImpl implements IPowDriverService {

    private final OprCacheManager oprCacheManager;
    private final PowDriverMapper baseMapper;
    private final PowDriverCertsMapper driverCertsMapper;
    private final PowAuditRecordMapper auditRecordMapper;
    private final PowDriverRateMapper driverRateMapper;
    private final PowAgentMapper agentMapper;
    private final PowGroupMapper groupMapper;
    private final PowDriverAccountMapper accountMapper;
    private final PowCarMapper carMapper;
    private final IPowCarService powCarService;
    private final PowDriverLineMapper driverLineMapper;
    private final PowAgentUserMapper agentUserMapper;
    private final IPowAgentService powAgentService;
    private final IPowGroupService powGroupService;
    private final IPowDriverVoucherService powDriverVoucherService;
    private final IPowDriverAccountService powDriverAccountService;

    @DubboReference
    private final RemoteDrvWalletService remoteDrvWalletService;
    @DubboReference
    private final RemoteAuthService remoteAuthService;
    @DubboReference
    private final RemoteRobService remoteRobService;
    @DubboReference
    private final RemoteFileService remoteFileService;
    @DubboReference
    private final RemoteConfigService remoteConfigService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    @DubboReference
    private final RemoteCityService remoteCityService;

    @Override
    public PowDriverVo getInfoById(Long id) {
        PowDriver powDriver = baseMapper.selectById(id);
        Map<Long, String> agentMap = getAgentMap(Collections.singletonList(powDriver.getAgentId()));
        // 转换
        PowDriverVo powDriverVo = MapstructUtils.convert(powDriver, PowDriverVo.class);
        powDriverVo.setAgentName(agentMap.get(powDriverVo.getAgentId()));
        powDriverVo.setSetPwd(powDriver.getCapitalPassword() == null);
        powDriverVo.setPhone(DesensitizedUtil.mobilePhone(powDriverVo.getPhone()));
        // 钱包信息
        RemoteWalletVo wallet = remoteDrvWalletService.getWallet(powDriver.getId());
        powDriverVo.setBalance(wallet.getBalance());
        powDriverVo.setFreeze(wallet.getFreeze());
        powDriverVo.setAmount(NumberUtil.add(wallet.getBalance(), wallet.getFreeze()).longValue());
        String refreshToken = remoteAuthService.refreshToken(LoginHelper.getLoginId(UserTypeEnum.DRIVER_USER.getUserType(), powDriver.getId()));
        if (refreshToken != null) {
            powDriverVo.setRefreshToken(refreshToken);
        }
        String openId = powDriverVoucherService.getOpenId(id);
        powDriverVo.setOpenId(openId);
        return powDriverVo;
    }

    /**
     * 生成邀请码
     *
     * @return
     */
    private String makeCode() {
        String code = RandomUtil.randomString(8);
        PowDriver exists = baseMapper.getByCode(code);
        if (ObjectUtils.isNotNull(exists)) {
            return makeCode();
        }
        return code;
    }

    /**
     * 查询司机
     *
     * @param id 主键
     * @return 司机
     */
    @Override
    public PowDriverVo queryById(Long id) {
        PowDriverVo powDriverVo = baseMapper.selectVoById(id);
        //司机组
        PowGroup powGroup = groupMapper.selectById(powDriverVo.getGroupId());
        if (powGroup != null) {
            powDriverVo.setGroupId(powGroup.getId());
            powDriverVo.setGroupType(powGroup.getType());
            powDriverVo.setGroupName(powGroup.getName());
        }
        //佣金比例
        PowDriverRate powDriverRate = driverRateMapper.queryByDriverId(id);
        if (ObjectUtil.isNotNull(powDriverRate)) {
            powDriverVo.setRate(powDriverRate.getRate());
        }
        //代理商
        PowAgent powAgent = agentMapper.selectById(powDriverVo.getAgentId());
        if (powAgent != null) {
            powDriverVo.setAgentName(powAgent.getCompanyName());
        }
        // 获取证件信息
        List<PowDriverCerts> driverCerts = driverCertsMapper.listByDriverId(id);
        if (CollUtils.isNotEmpty(driverCerts)) {
            // 将证件信息按照类型分组
            Map<String, PowDriverCerts> type2CertMap = driverCerts.stream().collect(Collectors.toMap(PowDriverCerts::getType, Function.identity(), (v1, v2) -> v1));
            String ossIds = driverCerts.stream().map(e -> e.getOssId().toString()).collect(Collectors.joining(","));
            // 获取证件的附件信息，并根据ossId进行分组
            Map<Long, String> ossId2Url = remoteFileService.selectByIds(ossIds)
                    .stream().collect(Collectors.toMap(RemoteFile::getOssId, RemoteFile::getUrl));

            // 身份证证件信息
            PowDriverCerts idCardCerts = type2CertMap.get(DrvCertTypeEnum.ID_FRONT.getCode());
            PowDriverCerts idCardBackCerts = type2CertMap.get(DrvCertTypeEnum.ID_BACK.getCode());
            if (ObjectUtils.isNotNull(idCardCerts)) {
                powDriverVo.setAddress(idCardCerts.getAddress())
                        .setFrontOssId(idCardCerts.getOssId())
                        .setFrontImg(ossId2Url.get(idCardCerts.getOssId()))
                        .setEndTime(idCardCerts.getEndTime())
                        .setBackImg(idCardBackCerts != null ? ossId2Url.get(idCardBackCerts.getOssId()) : null)
                        .setBackOssId(idCardBackCerts != null ? idCardBackCerts.getOssId() : null);
            }

            if (powDriverVo.getType() != null && DriverTypeEnum.SELF.getCode().equals(powDriverVo.getType())) {
                // 自营代扣协议
                PowDriverCerts agreement = type2CertMap.get(DrvCertTypeEnum.AGREEMENT.getCode());
                PowDriverCerts heldId = type2CertMap.get(DrvCertTypeEnum.HELD_ID.getCode());
                PowDriverCerts heldAgreement = type2CertMap.get(DrvCertTypeEnum.HELD_AGREEMENT.getCode());
                PowDriverAgreementVo powDriverAgreementVo = new PowDriverAgreementVo();
                powDriverAgreementVo.setAgreementOssId(agreement != null ? agreement.getOssId() : null);
                powDriverAgreementVo.setHeldIdCardOssId(heldId != null ? heldId.getOssId() : null);
                powDriverAgreementVo.setHeldAgreementOssId(heldAgreement != null ? heldAgreement.getOssId() : null);
                powDriverAgreementVo.setAgreementImg(agreement != null ? ossId2Url.get(agreement.getOssId()) : null);
                powDriverAgreementVo.setHeldIdCardImg(heldId != null ? ossId2Url.get(heldId.getOssId()) : null);
                powDriverAgreementVo.setHeldAgreementImg(heldAgreement != null ? ossId2Url.get(heldAgreement.getOssId()) : null);
                powDriverVo.setAgreementVo(powDriverAgreementVo);
            }

            // 驾驶证信息
            PowDriverCerts drivingCerts = type2CertMap.get(DrvCertTypeEnum.DRIVING.getCode());
            PowDriverCerts backDrivingCerts = type2CertMap.get(DrvCertTypeEnum.DRIVING_BACK.getCode());
            PowDrvDrivingVo driverDriving = new PowDrvDrivingVo();
            driverDriving.setCertsOwner(drivingCerts.getCertsOwner())
                    .setApprovedType(powDriverVo.getApprovedType())
                    .setFirstTime(drivingCerts.getFirstTime())
                    .setEndTime(drivingCerts.getEndTime())
                    .setFrontImg(ossId2Url.get(drivingCerts.getOssId()))
                    .setFrontOssId(drivingCerts.getOssId())
                    .setBackOssId(backDrivingCerts != null ? backDrivingCerts.getOssId() : null)
                    .setBackImg(backDrivingCerts != null ? ossId2Url.get(backDrivingCerts.getOssId()) : null);

            powDriverVo.setDrivingVo(driverDriving);

            // 获取车辆信息
            PowCarVo pxCarVo = powCarService.queryByDriver(id);
            // 车辆信息
            PowDriverCarVo driverCar = BeanUtils.copyProperties(pxCarVo, PowDriverCarVo.class);
            // 车辆证件信息
            PowDriverCerts carCert = type2CertMap.get(DrvCertTypeEnum.CAR.getCode());
            // 保险信息
            PowDriverCerts policyCert = type2CertMap.get(DrvCertTypeEnum.POLICY.getCode());
            // 行驶证证件信息
            PowDriverCerts licenseCert = type2CertMap.get(DrvCertTypeEnum.LICENSE.getCode());
            PowDriverCerts licenseBackCert = type2CertMap.get(DrvCertTypeEnum.LICENSE_BACK.getCode());
            driverCar.setCarOwner(carCert.getCertsOwner())
                    .setFirstTime(licenseCert.getFirstTime())
                    .setTravelFrontOssId(licenseCert.getOssId())
                    .setLicenseFrontImg(ossId2Url.get(licenseCert.getOssId()))
                    .setTravelBackOssId(licenseBackCert != null ? licenseBackCert.getOssId() : null)
                    .setLicenseBackImg(licenseBackCert != null ? ossId2Url.get(licenseBackCert.getOssId()) : null)
                    .setInsuranceOssId(policyCert != null ? policyCert.getOssId() : null)
                    .setPolicyImg(policyCert != null ? ossId2Url.get(policyCert.getOssId()) : null)
                    .setCarOssId(carCert.getOssId())
                    .setCarPic(ossId2Url.get(carCert.getOssId()));

            powDriverVo.setCarVo(driverCar);
        }

        // 支付宝账户信息
        PowDriverAccountVo driverAccountVo = powDriverAccountService.queryByDriverId(id);
        if (ObjectUtils.isNotNull(driverAccountVo)) {
            powDriverVo.setAccount(driverAccountVo.getAccount());
            powDriverVo.setAccountName(driverAccountVo.getName());
        }

        return powDriverVo;
    }

    /**
     * 获取代理商信息
     *
     * @param agentIds
     * @return
     */
    private Map<Long, String> getAgentMap(List<Long> agentIds) {
        List<PowAgent> powAgents = getAgentInfo(agentIds);
        return powAgents.stream().collect(Collectors.toMap(PowAgent::getId, PowAgent::getCompanyName, (v1, v2) -> v1));
    }

    /**
     * 获取代理商信息
     *
     * @param agentIds 代理商id集合
     * @return 代理商信息
     */
    private List<PowAgent> getAgentInfo(List<Long> agentIds) {
        List<PowAgent> powAgents = new ArrayList<>();
        List<Long> notExistCacheIds = new ArrayList<>();
        agentIds.forEach(agentId -> {
            String key = PowCacheKeyEnum.POW_AGENT_INFO_CACHE_KEY.create(agentId);
            if (RedisUtils.hasKey(key)) {
                powAgents.add(BeanUtils.copyProperties(RedisUtils.getCacheObject(key), PowAgent.class));
            } else {
                notExistCacheIds.add(agentId);
            }
        });

        if (CollUtil.isNotEmpty(notExistCacheIds)) {
            powAgents.addAll(powAgentService.queryByIdsAndCacheInfo(notExistCacheIds));
        }

        return powAgents;
    }

    /**
     * 获取佣金比例
     */
    private Map<Long, BigDecimal> getRateMap(List<Long> ids) {
        Map<Long, BigDecimal> rateMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ids)) {
            List<PowDriverRate> driverRates = driverRateMapper.queryByDriverIds(ids);
            for (PowDriverRate driverRate : driverRates) {
                rateMap.put(driverRate.getDriverId(), driverRate.getRate());
            }
        }
        return rateMap;

    }

    /**
     * 获取车牌号
     */
    public Map<Long, String> getCarMap(List<Long> ids) {
        Map<Long, String> carMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ids)) {
            List<PowCarVo> cars = powCarService.queryByDriverIds(ids);
            for (PowCarVo car : cars) {
                carMap.put(car.getDriverId(), car.getCarNumber());
            }
        }
        return carMap;
    }

    /**
     * 获取账户信息
     *
     * @param ids
     * @return
     */
    private Map<Long, PowDriverAccountVo> getAccountMap(List<Long> ids) {
        List<PowDriverAccountVo> driverAccountVoList = powDriverAccountService.queryByDriverIds(ids);
        Map<Long, PowDriverAccountVo> accountMap = driverAccountVoList.stream()
                .collect(Collectors.toMap(PowDriverAccountVo::getDriverId, Function.identity()));
        return accountMap;
    }

    /**
     * 获取分组信息
     *
     * @param groupIds 分组id集合
     * @return
     */
    private Map<Long, Map<String, String>> getGroupMap(List<Long> groupIds) {
        if (CollUtil.isNotEmpty(groupIds)) {
            List<PowGroup> groups = powGroupService.getGroupInfo(groupIds);
            return groups.stream().collect(Collectors.toMap(
                    PowGroup::getId,
                    group -> Map.of("name", group.getName(), "type", group.getType()),
                    (v1, v2) -> v1));
        }
        return new HashMap<>();
    }

    /**
     * 分页查询司机列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 司机分页列表
     */
    @Override
    public TableDataInfo<PowDriverVo> queryPageList(PowDriverBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PowDriver> lqw = buildQueryWrapper(bo);

        Page<PowDriver> result = baseMapper.selectPage(pageQuery.build(), lqw);
        List<PowDriverVo> records = MapstructUtils.convert(result.getRecords(), PowDriverVo.class);

        if (CollUtil.isNotEmpty(records)) {
            assembleData(records);
            /*Map<Long, BigDecimal> rateMap = getRateMap(records.stream().map(PowDriverVo::getId).toList());
            Map<Long, String> carMap = getCarMap(records.stream().map(PowDriverVo::getId).toList());
            Map<Long, PowDriverAccountVo> accountMap = getAccountMap(records.stream().map(PowDriverVo::getId).toList());
            Map<Long, String> agentMap = getAgentMap(records.stream().map(PowDriverVo::getAgentId).toList());
            Map<Long, Map<String, String>> groupMap = getGroupMap(records.stream().map(PowDriverVo::getGroupId).toList());
            records.forEach(item -> {
                item.setRate(rateMap.get(item.getId()));
                item.setCarNumber(carMap.get(item.getId()));
                item.setAgentName(agentMap.get(item.getAgentId()));
                Map<String, String> groupInfo = groupMap.get(item.getGroupId());
                if (groupInfo != null) {
                    item.setGroupName(groupInfo.get("name"));
                    item.setGroupType(groupInfo.get("type"));
                }
                PowDriverAccountVo accountVo = accountMap.get(item.getId());
                if (accountVo != null) {
                    item.setAccount(accountVo.getAccount());
                    item.setAccountName(accountVo.getName());
                }
            });*/
            if (bo.getCarNumber() != null) {
                records = records.stream().filter(item -> item.getCarNumber() != null && item.getCarNumber().contains(bo.getCarNumber()))
                        .collect(Collectors.toList());
            }

            //待出现数查询
            if (bo.getQueryPendingTripOrders()) {
                List<Long> driverIds = records.stream().map(PowDriverVo::getId).toList();
                Map<Long, Long> countMap = remoteOrderService.queryOrderCountByDriverIdsAndStatuses(driverIds, OrderStatusTabEnum.NO_GO.getStatusList());
                records.forEach(item -> {
                    item.setPendingTripOrdersNum(countMap.getOrDefault(item.getId(), 0L));
                });
            }

        }
        return new TableDataInfo<>(records, result.getTotal());
    }

    private void assembleData(List<PowDriverVo> records) {
        if (CollUtil.isNotEmpty(records)) {
            Map<Long, BigDecimal> rateMap = getRateMap(records.stream().map(PowDriverVo::getId).toList());
            Map<Long, String> carMap = getCarMap(records.stream().map(PowDriverVo::getId).toList());
            Map<Long, PowDriverAccountVo> accountMap = getAccountMap(records.stream().map(PowDriverVo::getId).toList());
            Map<Long, String> agentMap = getAgentMap(records.stream().map(PowDriverVo::getAgentId).toList());
            Map<Long, Map<String, String>> groupMap = getGroupMap(records.stream().map(PowDriverVo::getGroupId).toList());
            records.forEach(item -> {
                item.setRate(rateMap.get(item.getId()));
                item.setCarNumber(carMap.get(item.getId()));
                item.setAgentName(agentMap.get(item.getAgentId()));
                Map<String, String> groupInfo = groupMap.get(item.getGroupId());
                if (groupInfo != null) {
                    item.setGroupName(groupInfo.get("name"));
                    item.setGroupType(groupInfo.get("type"));
                }
                PowDriverAccountVo accountVo = accountMap.get(item.getId());
                if (accountVo != null) {
                    item.setAccount(accountVo.getAccount());
                    item.setAccountName(accountVo.getName());
                }
            });
        }
    }

    /**
     * 查询符合条件的司机列表
     *
     * @param bo 查询条件
     * @return 司机列表
     */
    @Override
    public List<PowDriverVo> queryList(PowDriverBo bo) {
        LambdaQueryWrapper<PowDriver> lqw = buildQueryWrapper(bo);
        List<PowDriverVo> records = baseMapper.selectVoList(lqw);
        if (CollUtil.isNotEmpty(records)) {
            /*Set<Long> agents = records.stream().map(PowDriverVo::getAgentId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(agents)) {
                List<PowAgent> powAgents = agentMapper.selectBatchIds(agents);
                if (CollUtil.isNotEmpty(powAgents)) {
                    Map<Long, String> agentMap = powAgents.stream().collect(Collectors.toMap(PowAgent::getId, PowAgent::getCompanyName));
                    records.forEach(item -> {
                        item.setAgentName(agentMap.get(item.getAgentId()));
                    });
                }
            }*/
            assembleData(records);
            if (bo.getCarNumber() != null) {
                records = records.stream().filter(item -> item.getCarNumber() != null && item.getCarNumber().contains(bo.getCarNumber()))
                        .collect(Collectors.toList());
            }
        }
        return records;
    }

    private LambdaQueryWrapper<PowDriver> buildQueryWrapper(PowDriverBo bo) {
        LambdaQueryWrapper<PowDriver> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(bo.getUnionId())) {
            lqw.nested(l -> {
                l.like(PowDriver::getId, bo.getUnionId()).or()
                        .like(PowDriver::getName, bo.getUnionId()).or()
                        .like(PowDriver::getPhone, bo.getUnionId()).or()
                        .like(PowDriver::getCardNo, bo.getUnionId())
                ;
            });
        }
        lqw.eq(StringUtils.isNotBlank(bo.getAuditStatus()), PowDriver::getAuditStatus, bo.getAuditStatus());
        lqw.eq(bo.getPhone() != null, PowDriver::getPhone, bo.getPhone());
        lqw.eq(bo.getAgentId() != null && bo.getAgentId() > 0, PowDriver::getAgentId, bo.getAgentId());
        lqw.eq(bo.getCityCode() != null, PowDriver::getCityCode, bo.getCityCode());
        lqw.ne(bo.getExcludedId() != null, PowDriver::getId, bo.getExcludedId());
        lqw.in(bo.getIds() != null, PowDriver::getId, bo.getIds());
        lqw.eq(bo.getGroupId() != null, PowDriver::getGroupId, bo.getGroupId());
        lqw.eq(bo.getParentId() != null, PowDriver::getParentId, bo.getParentId());
        lqw.eq(StrUtil.isNotBlank(bo.getType()), PowDriver::getType, bo.getType());
        lqw.in(!CollectionUtils.isEmpty(bo.getAgentIds()), PowDriver::getAgentId, bo.getAgentIds());
        lqw.eq(ObjectUtils.isNotNull(bo.getStatus()), PowDriver::getStatus, bo.getStatus());
        lqw.between(ObjectUtil.isNotNull(bo.getStartTime()) && ObjectUtil.isNotNull(bo.getEndTime()),
                PowDriver::getAuditTime, bo.getStartTime(), bo.getEndTime());
        lqw.like(StrUtil.isNotBlank(bo.getName()), PowDriver::getName, bo.getName());
        lqw.eq(StrUtil.isNotBlank(bo.getCode()), PowDriver::getCode, bo.getCode());
        return lqw;
    }

    /**
     * 新增司机
     *
     * @param bo 司机
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PowDriverBo bo) {
        PowDriver add = MapstructUtils.convert(bo, PowDriver.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改司机
     *
     * @param bo 司机
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PowDriverBo bo) {
        PowDriver update = baseMapper.selectById(bo.getId());
        Assert.notNull(update, "司机不存在");
        // 是否更换代理商
        boolean changeAgent = !ObjUtil.equal(update.getAgentId(), bo.getAgentId());
        update = MapstructUtils.convert(bo, update);
        validEntityBeforeSave(update);

        // 修改手机号，需要验证当前司机没有待完成的订单
        if (StringUtils.isNotBlank(bo.getPhone()) && !Objects.equals(update.getPhone(), bo.getPhone())) {
            validEntityBeforeUpdatePhone(bo.getId(), bo.getPhone());
        }
        //校验传参的司机组是否属于代理商
        if (bo.getAgentId() != null) {
            validGroupBeforeUpdate(bo);
        }

        LambdaUpdateWrapper<PowDriver> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(StringUtils.isNotBlank(bo.getPhone()), PowDriver::getPhone, bo.getPhone())
                .set(StringUtils.isNotBlank(bo.getCityCode()), PowDriver::getCityCode, bo.getCityCode())
                .set(bo.getAgentId() != null, PowDriver::getAgentId, bo.getAgentId())
                .set(bo.getGroupId() != null, PowDriver::getGroupId, bo.getGroupId())
                .set(bo.getResellServiceRate() != null, PowDriver::getResellServiceRate, bo.getResellServiceRate())
                .set(PowDriver::getUpdateTime, DateUtils.getNowDate())
                .eq(PowDriver::getId, bo.getId());

        // 更换代理商需要清空原有线路、清空抢单、移除代理抢单中的该司机id
        if (changeAgent) {
            validBeforeChangeAgent(bo.getId());
            driverLineMapper.deleteByDriverId(bo.getId());
            remoteRobService.switchAgent(bo.getId());

            // 司机类型
            String driverType = getDriverTypeByGroupId(bo.getGroupId());
            updateWrapper.set(StringUtils.isNotBlank(driverType), PowDriver::getType, driverType);
        }
        boolean flag = baseMapper.update(updateWrapper) > 0;

        if (flag) {
            String loginId = LoginHelper.getLoginId(UserTypeEnum.DRIVER_USER.getUserType(), update.getId());
            ThreadUtil.execAsync(() -> remoteAuthService.logout(loginId));
        }
        //佣金比例
        if (bo.getRate() != null) {
            PowDriverRate powDriverRate = driverRateMapper.queryByDriverId(bo.getId());
            if (powDriverRate == null) {
                PowDriverRate driverRate = BeanUtils.copyProperties(bo, PowDriverRate.class);
                driverRate.setPlatformCode(PlatformCodeEnum.TY.getCode());
                driverRateMapper.insert(driverRate);
            } else {
                driverRateMapper.updateRate(bo.getId(), bo.getRate());
                try {
                    // 删除司机利率缓存
                    String cacheKey = PowCacheKeyEnum.POW_DRIVER_RATE_CACHE_KEY.create(bo.getId(), PlatformCodeEnum.TY.getCode());
                    RedisUtils.deleteObject(cacheKey);
                } catch (Exception e) {
                    log.error("删除司机利率缓存失败");
                }
            }
        }
        // 司机账号
        if (StringUtils.isNotBlank(bo.getAccount()) || StringUtils.isNotBlank(bo.getAccountName())) {
            PowDriverAccountVo powDriverAccountVo = powDriverAccountService.queryByDriverId(bo.getId());
            if (powDriverAccountVo != null) {
                powDriverAccountService.updateAccountAndNameByDriverId(bo.getAccount(), bo.getAccountName(), update.getId());
            } else {
                PowDriverAccount newDriverAccount = new PowDriverAccount();
                newDriverAccount.setDriverId(bo.getId())
                        .setName(bo.getAccountName())
                        .setAccount(bo.getAccount())
                        .setPhone(bo.getPhone())
                        .setDefaulted(IsYesEnum.YES.getCode())
                        .setStatus(StatusEnum.ENABLE.getCode())
                        .setType(AccountTypeEnum.ALIPAY.getCode());
                accountMapper.insert(newDriverAccount);
            }
        }

        // 删除缓存
        this.deleteDriverCache(bo.getId());
        return flag;
    }

    private void validGroupBeforeUpdate(PowDriverBo bo) {
        LambdaQueryWrapper<PowGroup> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PowGroup::getAgentId, bo.getAgentId());
        List<PowGroup> powGroups = groupMapper.selectList(lqw);
        if (CollUtil.isNotEmpty(powGroups)) {
            List<Long> groupIds = powGroups.stream().map(PowGroup::getId).toList();
            if (!groupIds.contains(bo.getGroupId())) {
                throw new ServiceException("司机组不属于代理商，请重新选择");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PowDriverVo login(RemoteDrvLoginVo remoteDrvLoginVo) {
        PowDriver powDriver = baseMapper.getByPhone(remoteDrvLoginVo.getPhone());
        PowDriverVo powDriverVo = MapstructUtils.convert(powDriver, PowDriverVo.class);
        if (powDriverVo == null) {
            /// 注册
            PowDriver register = register(remoteDrvLoginVo);
            powDriverVo = BeanUtil.copyProperties(register, PowDriverVo.class);
        }

        if (powDriverVo == null) {
            throw new ServiceException("登录异常，请联系管理员");
        }
        if (remoteDrvLoginVo.getAgentId() != null && remoteDrvLoginVo.getGroupId() != null) {
            LambdaQueryWrapper<PowAuditRecord> auditLqw = new LambdaQueryWrapper<>();
            auditLqw.eq(PowAuditRecord::getDriverId, powDriverVo.getId());
            Long l = auditRecordMapper.selectCount(auditLqw);
            if (l == 0) {
                //更新司机上级
                LambdaUpdateWrapper<PowDriver> lqw = new LambdaUpdateWrapper<>();
                lqw.eq(PowDriver::getId, powDriverVo.getId()).set(PowDriver::getAgentId, remoteDrvLoginVo.getAgentId()).set(PowDriver::getGroupId, remoteDrvLoginVo.getGroupId());
                baseMapper.update(lqw);
            }
        }

        saveVoucher(remoteDrvLoginVo, powDriverVo);

        powDriverVo.setOpenId(remoteDrvLoginVo.getOpenId());
        return powDriverVo;
    }

    private void saveVoucher(RemoteDrvLoginVo remoteDrvLoginVo, PowDriverVo powDriverVo) {
        // 司机凭证入库
        PowDriverVoucherBo powDriverVoucherBo = new PowDriverVoucherBo();
        powDriverVoucherBo.setTenantId(remoteDrvLoginVo.getTenantId());
        powDriverVoucherBo.setDriverId(powDriverVo.getId());
        powDriverVoucherBo.setType(remoteDrvLoginVo.getType());
        powDriverVoucherBo.setAppId(remoteDrvLoginVo.getAppId());
        powDriverVoucherBo.setOpenId(remoteDrvLoginVo.getOpenId());
        powDriverVoucherBo.setUnionId(remoteDrvLoginVo.getUnionId());
        powDriverVoucherBo.setStatus(StatusEnum.ENABLE.getCode());
        powDriverVoucherService.insertByBo(powDriverVoucherBo);
    }

    /**
     * 司机注册
     *
     * @param remoteDrvLoginVo
     * @return
     */
    private PowDriver register(RemoteDrvLoginVo remoteDrvLoginVo) {
        PowDriver powDriver = new PowDriver();
        powDriver.setPhone(remoteDrvLoginVo.getPhone());
        powDriver.setTenantId(remoteDrvLoginVo.getTenantId());
        powDriver.setCode(makeCode());
        powDriver.setSource(remoteDrvLoginVo.getSource());
        powDriver.setIdentity(DriverIdentityEnum.NO.getCode());
        powDriver.setAuditStatus(DrvAuditStatusEnum.NO.getCode());
        powDriver.setReceive(IsYesEnum.YES.getCode());
        powDriver.setStatus(UserStatusEnum.OK.getCode());
        if (remoteDrvLoginVo.getAgentId() != null) {
            powDriver.setAgentId(remoteDrvLoginVo.getAgentId());
        }
        if (remoteDrvLoginVo.getGroupId() != null) {
            powDriver.setGroupId(remoteDrvLoginVo.getGroupId());
        }
        validEntityBeforeSave(powDriver);
        baseMapper.insert(powDriver);
        return powDriver;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateStatus(Long id, String status) {
        PowDriver powDriver = baseMapper.selectById(id);
        if (!powDriver.getStatus().equals(status) && UserStatusEnum.getInfoByCode(status) != null) {
            //司机账号启用，钱包同时启用
            if (Objects.equals(UserStatusEnum.OK.getCode(), status)) {
                remoteDrvWalletService.updateWallerStatus(id, status);
            }
            powDriver.setStatus(status);
            boolean ret = baseMapper.updateById(powDriver) > 0;
            if (ret && UserStatusEnum.DISABLE.getCode().equals(status)) {
                disableDriver(id);
                // 禁用需要下线
                String loginId = LoginHelper.getLoginId(UserTypeEnum.DRIVER_USER.getUserType(), id);
                ThreadUtil.execAsync(() -> remoteAuthService.logout(loginId));
            }
            return ret;
        }
        return true;
    }

    private void disableDriver(Long driverId) {
        // 清除司机线路
        driverLineMapper.deleteByDriverId(driverId);
        // 禁用抢单
        remoteRobService.disableDriverRob(driverId);
    }

    /**
     * 获取司机信息 - 校验是否为司机
     *
     * @param id
     * @return
     */
    public PowDriverVo getDrivers(Long id) {
        PowDriverVo powDriverVo = queryById(id);
        if (ObjectUtils.isNull(powDriverVo)) {
            throw new ServiceException("信息不存在");
        }
        if (!powDriverVo.getIdentity().equals(DriverIdentityEnum.CROSS_DRV.getCode())) {
            throw new ServiceException("该账号未注册为顺风车司机");
        }
        return powDriverVo;
    }

    /**
     * 修改司机手机号
     */
    @Override
    public Boolean setPhone(PowDriverPhoneForm bo) {
        boolean verify = SmsUtil.verify(bo.getNewPhone(), SmsCodeTypeEnum.PHONE_CHANGE, bo.getSmsCode());
        Assert.isTrue(verify, SmsConstant.SMS_CAPTCHA_WRONG);
        PowDriver byPhone = baseMapper.getByPhone(bo.getNewPhone());
        Assert.isNull(byPhone, "当前手机号已被他人注册");
        boolean ret = baseMapper.updateAny(LoginHelper.getUserId(), PowDriver::getPhone, bo.getNewPhone()) > 0;
        if (ret) {
            // 删除验证码
            SmsUtil.remove(bo.getNewPhone(), SmsCodeTypeEnum.PHONE_CHANGE);
        }
        return ret;
    }

    /**
     * 重置司机资金密码
     */
    @Override
    public Boolean resetCapitalPassword(PowDriverCapitalPwdForm bo) {
        Long driverId = LoginHelper.getUserId();
        PowDriver driver = baseMapper.selectById(driverId);
        Assert.notNull(driver, "司机不存在");
        //Assert.isTrue(SmsUtil.verify(LoginHelper.getUserPhone(), SmsCodeTypeEnum.CAPITAL_PWD, bo.getSmsCode()), SmsConstant.SMS_CAPTCHA_WRONG);
        boolean flag = baseMapper.updatePwd(driverId, bo.getCapitalPassword()) > 0;
        //if (flag) {
        //    // 清除重置密码限制
        //    String reset = CacheKeyEnum.DRIVER_PWD_RESET_KEY.create(driverId);
        //    RedisUtils.deleteObject(reset);
        //    String err = CacheKeyEnum.DRIVER_PWD_ERR_TIMES_KEY.create(driverId);
        //    RedisUtils.deleteObject(err);
        //    // 删除验证码
        //    SmsUtil.remove(LoginHelper.getUserPhone(), SmsCodeTypeEnum.CAPITAL_PWD);
        //}
        return flag;
    }

    /**
     * 修改司机资金密码
     */
    @Override
    public Boolean changeCapitalPassword(PowDriverCapitalPwdForm bo) {
        Long driverId = LoginHelper.getUserId();
        String key = CacheKeyEnum.DRIVER_PWD_VERIFY_TOKEN_KEY.create(driverId, PwdVerifyEnum.RESET);
        String token = RedisUtils.getCacheObject(key);
        Assert.isTrue(token != null && token.equals(bo.getAccessToken()), "请求已失效，请重新验证交易密码");
        boolean flag = baseMapper.updatePwd(LoginHelper.getUserId(), bo.getCapitalPassword()) > 0;
        if (flag) {
            // 清除重置密码token
            RedisUtils.deleteObject(key);
            // 清除重置密码限制
            String reset = CacheKeyEnum.DRIVER_PWD_RESET_KEY.create(driverId);
            RedisUtils.deleteObject(reset);
            String err = CacheKeyEnum.DRIVER_PWD_ERR_TIMES_KEY.create(driverId);
            RedisUtils.deleteObject(err);
        }
        return flag;
    }

    /**
     * 验证司机资金密码
     */
    @Override
    public String verifyCapitalPassword(PowDriverCapitalPwdForm bo) {
        Long driverId = LoginHelper.getUserId();
        String timesKey = CacheKeyEnum.DRIVER_PWD_ERR_TIMES_KEY.create(driverId);
        Integer failTimes = RedisUtils.getCacheObject(timesKey);
        if (failTimes != null && failTimes >= FinanceConstants.PASSWORD_WRONG_MAX_TIMES) {
            String reset = CacheKeyEnum.DRIVER_PWD_RESET_KEY.create(driverId);
            RedisUtils.setObjectIfAbsent(reset, true);
            throw new ServiceException("交易密码多次错误，请修改交易密码");
        }

        PowDriver powDriver = baseMapper.selectById(driverId);
        boolean checkpw = BCrypt.checkpw(bo.getCapitalPassword(), powDriver.getCapitalPassword());
        if (!checkpw) {
            RedisUtils.addAndGet(timesKey, 1);
            throw new ServiceException("交易密码输入错误，请重新输入");
        } else {
            RedisUtils.deleteObject(timesKey);
            String tokenKey = CacheKeyEnum.DRIVER_PWD_VERIFY_TOKEN_KEY.create(driverId, bo.getVerifyType());
            String token = IdUtil.fastSimpleUUID();
            // 5分钟有效
            RedisUtils.setCacheObject(tokenKey, token, Duration.ofMinutes(5));
            return token;
        }
    }

    /**
     * 查询调度司机列表
     *
     * @param bo 调度参数
     * @return
     */
    @Override
    public TableDataInfo<PowDriverVo> dispatch(PowDriverBo bo, PageQuery pageQuery) {
        /// 构造查询条件实例
        PowDriverBo driverBo = BeanUtils.copyProperties(bo, PowDriverBo.class);
        // 调度的线路 --- 获取这个线路所有的司机
        if (bo.getLineId() != null && bo.getLineId() > 0) {
            List<PowDriverLine> driverLines = driverLineMapper.listByLineId(bo.getLineId());
            if (ObjectUtils.isNotNull(driverLines)) {
                List<Long> driverIds = StreamUtils.toList(driverLines, PowDriverLine::getDriverId);
                driverBo.setIds(driverIds);
            } else {
                return TableDataInfo.build();
            }
        }
        // 只查询代理商自己的司机
        if (ObjectUtil.isNotNull(bo.getAgentId()) && bo.getAgentId() > 0) {
            driverBo.setAgentIds(Collections.singletonList(bo.getAgentId()));
        }
        // 身份-司机
        driverBo.setIdentity(DriverIdentityEnum.CROSS_DRV.getCode());
        // 正常登录
        driverBo.setStatus(UserStatusEnum.OK.getCode());
        // 审核通过
        driverBo.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
        // 接受派单
        driverBo.setIsReceive(IsYesEnum.YES.getCode());

        return queryPageList(driverBo, pageQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDriver(MultipartFile file) {
        try (InputStream in = file.getInputStream()) {
            List<PowDriverImportBo> importBos = ExcelUtil.importExcel(in, PowDriverImportBo.class);
            if (CollUtil.isNotEmpty(importBos)) {
                List<PowAgent> agents = agentMapper.selectList();
                Map<String, PowAgent> agentMap = agents.stream().collect(Collectors.toMap(PowAgent::getCompanyName, Function.identity()));

                // 已有的司机
                List<PowDriver> allDrivers = baseMapper.selectList();
                Set<String> cardNos = allDrivers.stream().map(PowDriver::getCardNo).collect(Collectors.toSet());

                // 默认司机组
                PowGroup defaultGroup = groupMapper.getDefaultGroup();

                List<PowDriver> drivers = new ArrayList<>(importBos.size());
                for (PowDriverImportBo importBo : importBos) {
                    // 已有的跳过
                    if (cardNos.contains(importBo.getCardNo())) {
                        continue;
                    }

                    PowDriver driver = MapstructUtils.convert(importBo, PowDriver.class);
                    // 代理商信息
                    PowAgent agent = null;
                    String agentName = StrUtil.cleanBlank(importBo.getAgentName());
                    String replaced = agentName.replaceAll("（.*?）", "");
                    List<Map.Entry<String, PowAgent>> list = agentMap.entrySet().stream().filter(e -> e.getKey().contains(replaced)).toList();
                    if (CollUtil.isNotEmpty(list)) {
                        Map.Entry<String, PowAgent> agentEntry = list.get(0);
                        agent = agentEntry.getValue();
                    }
                    if (ObjectUtil.isNotNull(agent)) {
                        driver.setAgentId(agent.getId());
                    } else {
                        log.error(StrUtil.format("导入失败，代理{}不存在", replaced));
                    }
                    // 其他信息
                    // 是否是司机 是否已通过
                    driver.setDelFlag("0");
                    driver.setSource(SourceEnum.WX_MINI.getCode());
                    driver.setIdentity(DriverIdentityEnum.CROSS_DRV.getCode());
                    driver.setAuditStatus(DrvAuditStatusEnum.SUCCESS.getCode());
                    driver.setStatus(StatusEnum.ENABLE.getCode());
                    driver.setReceive(IsYesEnum.YES.getCode());
                    if (defaultGroup != null) {
                        driver.setGroupId(defaultGroup.getId());
                    }
                    drivers.add(driver);
                }
                TenantHelper.insertFill(drivers);
                baseMapper.insertBatchSomeColumn(drivers);

                Map<String, PowDriverImportBo> importBoMap = importBos.stream().collect(Collectors.toMap(PowDriverImportBo::getPhone, Function.identity()));
                // 账号信息
                List<PowDriverAccount> accounts = new ArrayList<>();
                for (PowDriver driver : drivers) {
                    PowDriverImportBo importBo = importBoMap.get(driver.getPhone());
                    if (StrUtil.isAllNotBlank(importBo.getAliName(), importBo.getAliPhone(), importBo.getAccount())) {
                        PowDriverAccount account = new PowDriverAccount();
                        account.setDelFlag("0");
                        account.setDriverId(driver.getId());
                        account.setName(importBo.getAliName());
                        account.setPhone(importBo.getAliPhone());
                        account.setAccount(importBo.getAccount());
                        account.setType(AccountTypeEnum.ALIPAY.getCode());
                        account.setDefaulted(IsYesEnum.YES.getCode());
                        account.setStatus(StatusEnum.ENABLE.getCode());
                        accounts.add(account);
                    }
                }
                if (CollUtil.isNotEmpty(accounts)) {
                    TenantHelper.insertFill(accounts);
                    accountMapper.insertBatchSomeColumn(accounts);
                }

                // 车辆信息
                List<PowCar> cars = new ArrayList<>(drivers.size());
                for (PowDriver driver : drivers) {
                    PowDriverImportBo importBo = importBoMap.get(driver.getPhone());
                    if (ObjectUtil.isNotNull(importBo)) {
                        PowCar car = MapstructUtils.convert(importBo, PowCar.class);
                        car.setDelFlag("0");
                        car.setDriverId(driver.getId());
                        car.setSeat(5L);
                        car.setStatus(StatusEnum.ENABLE.getCode());
                        cars.add(car);
                    }
                }
                TenantHelper.insertFill(cars);
                carMapper.insertBatchSomeColumn(cars);
                // 钱包信息等登录的时候再开
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("导入失败");
        }
    }

    /**
     * 司机邀请码
     *
     * @return
     */
    @Override
    public PowDriverVo generateInviteCode() {
        Long driverId = LoginHelper.getUserId();
        PowDriverVo driver = baseMapper.selectVoById(driverId);
        org.springframework.util.Assert.notNull(driver, "司机信息不存在");
        String inviteCode = driver.getCode();
        if (StrUtil.isBlank(inviteCode)) {
            driver.setCode(makeCode());
        }
        UserTypeEnum userType = LoginHelper.getUserType();
        //邀请链接
        String inviteUrl = remoteConfigService.selectValueByKey(PowerConstants.DRIVER_INVITE_URL) + inviteCode + "&userType=" + userType.name();
        //邀请二维码
        BufferedImage image = QrCodeUtil.generate(inviteUrl, 200, 200);
        // 创建 ByteArrayOutputStream 用来保存图像的字节流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", byteArrayOutputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        RemoteFile remoteFile = remoteFileService.upload(driver.getPhone(), driver.getPhone() + ".jpg", image.toString(), byteArrayOutputStream.toByteArray());
        RemoteFile remoteFile1 = remoteFileService.selectById(remoteFile.getOssId());
        PowDriverVo driverVo = new PowDriverVo();
        driverVo.setInviteUrl(inviteUrl);
        driverVo.setInviteImage(remoteFile1.getUrl());
        return driverVo;
    }

    /**
     * 修改司机组
     *
     * @param bo 修改司机组参数
     * @return 是否修改成功
     */
    @Override
    public Boolean updateDriverGroup(PowDriverBo bo) {
        PowGroup powGroup = groupMapper.selectById(bo.getGroupId());
        LambdaUpdateWrapper<PowDriver> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PowDriver::getGroupId, bo.getGroupId())
                .set(PowDriver::getType, powGroup.getType())
                .set(PowDriver::getUpdateTime, DateUtils.getNowDate())
                .eq(PowDriver::getId, bo.getId());

        // 删除司机缓存
        deleteDriverCache(bo.getId());

        return baseMapper.update(updateWrapper) > 0;
    }

    @Override
    public String generateInvitePrizeCode(Long userId) {
        UserTypeEnum userType = LoginHelper.getUserType();
        String inviteCode = null;
        if (UserTypeEnum.DRIVER_USER.getUserType().equals(userType.getUserType())) {
            PowDriverVo driver = baseMapper.selectVoById(userId);
            org.springframework.util.Assert.notNull(driver, "司机信息不存在");
            inviteCode = driver.getCode();
        } else if (UserTypeEnum.AGENT_USER.getUserType().equals(userType.getUserType())) {
            PowAgentUser powAgentUser = agentUserMapper.selectById(userId);
            org.springframework.util.Assert.notNull(powAgentUser, "代理商信息不存在");
            inviteCode = powAgentUser.getInviteCode();
        }
        //邀请链接
        String inviteUrl = remoteConfigService.selectValueByKey(PowerConstants.INVITE_PRIZE_REFERRAL_URL) + "?params=" + userType.getUserType() + "&" + inviteCode;
        //邀请二维码
        BufferedImage image = QrCodeUtil.generate(inviteUrl, 200, 200);
        // 创建 ByteArrayOutputStream 用来保存图像的字节流
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", byteArrayOutputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        RemoteFile remoteFile = remoteFileService.uploadToPublic(inviteCode, inviteCode + ".jpg", image.toString(), byteArrayOutputStream.toByteArray());
        return remoteFile.getUrl();
    }

    /**
     * 订单转卖司机接单列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @Override
    public TableDataInfo<PowDriverVo> queryResellPageList(PowDriverBo bo, PageQuery pageQuery) {
        // 仅支持指定除自己之外的且司机为当前司机组的
        PowDriver driver = baseMapper.selectById(LoginHelper.getUserId());
        bo.setGroupId(driver != null ? driver.getGroupId() : null);
        LambdaQueryWrapper<PowDriver> lqw = buildQueryWrapper(bo);
        Page<PowDriverVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<Long> driverIds = result.getRecords().stream().map(PowDriverVo::getId).toList();
        if (CollUtil.isNotEmpty(driverIds)) {
            Map<Long, String> driverId2CarNumberMap = powCarService.queryByDriverIds(driverIds)
                    .stream().collect(Collectors.toMap(PowCarVo::getDriverId, PowCarVo::getCarNumber, (v1, v2) -> v2));
            result.getRecords().forEach(vo -> {
                vo.setCarNumber(driverId2CarNumberMap.get(vo.getId()));
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 司机注册时查询所在城市是否开城
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @return
     */
    @Override
    public String registerCheck(String longitude, String latitude) {
        TxMapReGeocode regeo = TxMapUtils.regeo(longitude, latitude);
        if (regeo != null) {
            String adCode = regeo.getAdInfo().getAdcode();
            DistrictCacheVo districtCacheVo = oprCacheManager.getDistrictCacheVByAdCode(adCode);
            if (districtCacheVo != null) {
                RemoteCityVo remoteCityVo = remoteCityService.queryByCityCode(districtCacheVo.getCityCode());
                return remoteCityVo != null ? IsYesEnum.YES.getCode() : IsYesEnum.NO.getCode();
            }
        }
        return IsYesEnum.NO.getCode();
    }

    /**
     * 修改司机接单状态（接受派单）
     *
     * @param id
     * @return
     */
    @Override
    public Boolean receiveStatus(Long id) {
        // 获取司机信息
        PowDriverVo powDriverVo = getDrivers(id);
        String isReceive = powDriverVo.getIsReceive();
        if (isReceive.equals(IsYesEnum.YES.getCode())) {
            isReceive = IsYesEnum.NO.getCode();
        } else {
            isReceive = IsYesEnum.YES.getCode();
        }
        // 修改信息
        LambdaUpdateWrapper<PowDriver> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(PowDriver::getReceive, isReceive)
                .eq(PowDriver::getId, id);
        return baseMapper.update(updateWrapper) > 0;
    }

    /**
     * 获取司机位置缓存的KEY
     *
     * @param driverId
     * @return
     */
    private String getDriverLogCacheKey(Long driverId) {
        return PowCacheKeyEnum.POW_DRIVER_LOCATION_CACHE_KEY.create(driverId);
    }

    /**
     * 获取司机位置
     *
     * @param driverId
     * @return
     */
    @Override
    public String getLoc(Long driverId) {
        String driverLogCacheKey = getDriverLogCacheKey(driverId);
        return RedisUtils.getCacheObject(driverLogCacheKey).toString();
    }

    /**
     * 司机 位置上报
     *
     * @param driverId
     * @param longitude
     * @param latitude
     * @return
     */
    @Override
    public void locReport(Long driverId, String longitude, String latitude) {
        String driverLogCacheKey = getDriverLogCacheKey(driverId);
        String value = longitude + "," + latitude;
        RedisUtils.setCacheObject(driverLogCacheKey, value, Duration.ofMinutes(1));
    }

    /**
     * 修改司机手机号前校验是否有未完成订单
     *
     * @param driverId 司机id
     * @param phone    司机手机号
     */
    private void validEntityBeforeUpdatePhone(Long driverId, String phone) {
        List<RemoteOrderVo> remoteOrderVos = remoteOrderService.queryNotFinishOrderByDriverId(driverId);
        if (CollUtil.isNotEmpty(remoteOrderVos)) {
            throw new ServiceException("司机有未完成的订单，暂不支持修改手机号");
        }
        Long count = baseMapper.selectCount(
                new LambdaQueryWrapper<PowDriver>()
                        .eq(PowDriver::getPhone, phone)
                        .ne(PowDriver::getId, driverId)
        );
        if (count > 0) {
            throw new ServiceException("该手机号已存在");
        }
    }

    private void validBeforeChangeAgent(Long driverId) {
        // 是否前代理商的订单都已经结算、不做判断
        // 是否提现余额为0，且无提现中的提现单
        RemoteWalletVo wallet = remoteDrvWalletService.getWallet(driverId);
        Assert.isTrue(wallet.getBalance() == 0L, "可提现金额不为0，暂不支持更换代理商");
        Assert.isTrue(wallet.getCashing() == 0L, "存在未完成的提现单，请完成后再次发起");
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PowDriver entity) {
        if (StringUtils.isEmpty(entity.getBirthday()) && StringUtils.isNotBlank(entity.getCardNo())) {
            String birthByIdCard = IdcardUtil.getBirthByIdCard(entity.getCardNo());
            entity.setBirthday(birthByIdCard);
        }
        if (entity.getId() != null) {
            PowDriver phone = baseMapper.getByPhone(entity.getPhone());
            Assert.isTrue(phone == null || phone.getId().equals(entity.getId()), "该手机号已注册");
        }
    }

    /**
     * 封禁司机
     *
     * @param id      待删除的主键
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean sealById(Long id, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        boolean updateFlag = baseMapper.sealById(id) > 0;

        // 删除缓存
        this.deleteDriverCache(id);

        return updateFlag;
    }

    /**
     * 解禁司机
     */
    @Override
    public Boolean unsealById(Long id) {
        boolean updateFlag = baseMapper.unsealById(id) > 0;
        // 删除缓存
        this.deleteDriverCache(id);
        return updateFlag;
    }

    /**
     * 根据司机组id获取司机类型
     *
     * @param groupId 司机组id
     * @return 司机类型
     */
    private String getDriverTypeByGroupId(Long groupId) {
        PowGroup powGroup = powGroupService.getGroupInfo(groupId);
        if (ObjectUtil.isNull(powGroup)) {
            throw new ServiceException("司机组不存在");
        }
        return DriverTypeEnum.getCodeByGroupType(powGroup.getType());
    }

    /**
     * 删除司机缓存
     *
     * @param driverId 司机id
     */
    private void deleteDriverCache(Long driverId) {
        if (driverId != null) {
            ExceptionUtil.ignoreEx(() -> {
                String cacheKey = PowCacheKeyEnum.POW_DRIVER_INFO_CACHE_KEY.create(driverId);
                if (RedisUtils.hasKey(cacheKey)) {
                    RedisUtils.deleteObject(cacheKey);
                }
            });
        }
    }
}
