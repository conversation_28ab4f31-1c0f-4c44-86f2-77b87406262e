package com.feidi.xx.cross.market.dubbo;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.xx.BeanUtils;
import com.feidi.xx.common.redis.utils.RedisUtils;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.market.enums.MktCacheKeyEnum;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.common.enums.message.CouponMessageTypeEnum;
import com.feidi.xx.cross.common.enums.message.WebSocketTypeEnum;
import com.feidi.xx.cross.market.api.RemoteActivityService;
import com.feidi.xx.cross.market.api.domain.RemoteActivityBo;
import com.feidi.xx.cross.market.api.domain.RemoteActivityVo;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.mapper.MktActivityMapper;
import com.feidi.xx.cross.market.service.IMktCouponGrantService;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.cross.message.api.RemoteWebSocketService;
import com.feidi.xx.cross.message.api.domain.dto.CouponMessage;
import com.feidi.xx.cross.message.api.domain.dto.WebSocketMessageWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 活动服务接口实现类
 *
 * <AUTHOR>
 * @date 2024/9/7
 */
@Slf4j
@Service
@DubboService
@RequiredArgsConstructor
public class RemoteActivityServiceImpl implements RemoteActivityService {

    private final MktActivityMapper baseMapper;
    private final IMktCouponService mktCouponService;
    private final IMktCouponGrantService mktCouponGrantService;
    private final ScheduledExecutorService scheduledExecutorService;

    @DubboReference
    private final RemoteWebSocketService remoteWebSocketService;


    @Override
    public void joinActivity(RemoteActivityBo remoteActivityBo) {
        log.debug("joinActivity p {}", remoteActivityBo);

        MktActivityVo mktActivityVo = null;
        if (ObjectUtils.isNotEmpty(remoteActivityBo.getCityCode())) {
            LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MktActivity::getScope, ActivityScopeEnum.NEW.getCode())
                    .like(MktActivity::getCityCode, remoteActivityBo.getCityCode())
                    .eq(MktActivity::getStatus, ActivityStatusEnum.STARTING.getCode())
                    .eq(MktActivity::getShelvesStatus, ShelvesStatusEnum.ONLINE.getCode())
                    // 开始时间 < 当前
                    .le(MktActivity::getStartTime, DateUtil.now())
                    // 结束时间 > 当前
                    .ge(MktActivity::getEndTime, DateUtil.now())
                    .orderByDesc(MktActivity::getCreateTime)
                    .last("limit 1");
            mktActivityVo = baseMapper.selectVoOne(queryWrapper);
        }
        log.debug("joinActivity mktActivityVo {}", mktActivityVo);
        if (ObjectUtils.isNotEmpty(mktActivityVo)) {
            /// 获取优惠券
            MktCouponBo couponBo = new MktCouponBo();
            couponBo.setCouponIds(new HashSet<>(mktActivityVo.getCouponIds()));
            couponBo.setPaidType(PaidTypeEnum.AUTO.getCode());
            couponBo.setStatus(CouponTemplateStatusEnum.ACTIVE.getCode());
            couponBo.setOnlyValid(true);//查询有效的
            List<MktCouponVo> mktCouponVos = mktCouponService.queryCoupon(couponBo);
            log.debug("joinActivity mktCouponVos {}", mktCouponVos);
            remoteActivityBo.setActivityId(mktActivityVo.getId());
            if (ObjectUtils.isNotEmpty(mktCouponVos)) {
                for (MktCouponVo mktCouponVo : mktCouponVos) {
                    if (remoteActivityBo.getActivityType().equals(ActivityTypeEnum.INVITE_NEW.getCode())){
                        //加锁逻辑
                        RedissonClient client = RedisUtils.getClient();
                        String lockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_LOCK_KEY.create(mktCouponVo.getActivityId());
                        RLock lock = client.getLock(lockKey);
                        try {
                            if (lock.tryLock( 10,TimeUnit.SECONDS)) {
                                mktCouponGrantService.grantCoupon(remoteActivityBo, mktCouponVo);
                            }

                        } catch (Exception e) {
                            log.info(e.getMessage(), e);
                        } finally {
                            if (lock.isLocked()) {
                                lock.unlock();
                            }
                        }
                    }else {
                        for (int i = 0; i < mktCouponVo.getMaxNum(); i++) {
                            //加锁逻辑
                            RedissonClient client = RedisUtils.getClient();
                            String lockKey = MktCacheKeyEnum.MKT_COUPON_STOCK_LOCK_KEY.create(mktCouponVo.getActivityId());
                            RLock lock = client.getLock(lockKey);
                            try {
                                if (lock.tryLock(10,TimeUnit.SECONDS)) {
                                    mktCouponGrantService.grantCoupon(remoteActivityBo, mktCouponVo);
                                }
                            } catch (Exception e) {
                               log.info(e.getMessage(), e);
                            } finally {
                                if (lock.isLocked()) {
                                    lock.unlock();
                                }
                            }
                        }
                    }
                }
                // 优惠券发放成功，进行WebSocket通知
                if (Objects.equals(remoteActivityBo.getSuccess(), true) && ObjectUtils.isNotEmpty(LoginHelper.getUserId())) {
                    scheduledExecutorService.schedule(() -> {
                        WebSocketMessageWrapper<CouponMessage> messageWrapper = new WebSocketMessageWrapper<>();
                        CouponMessage orderMessage = CouponMessage.builder()
                                .messageType(CouponMessageTypeEnum.NEW.getCode())
                                .message("新人专属优惠券已发放，快去下单体验吧")
                                .build();

                        messageWrapper.setType(WebSocketTypeEnum.ANNOUNCEMENT.getCode());
                        messageWrapper.setReceiverUserType(UserTypeEnum.PASSENGER_USER.getUserType());
                        messageWrapper.setReceiverId(String.valueOf(LoginHelper.getUserId()));
                        messageWrapper.setData(orderMessage);
                        remoteWebSocketService.sendMessage(messageWrapper);
                    }, 3, TimeUnit.SECONDS);
                }
            }
        }
    }

    /**
     * 根据活动id查询活动信息
     *
     * @param activityIds 活动id
     * @return 活动信息
     */
    @Override
    public List<RemoteActivityVo> queryByIds(Set<Long> activityIds) {
        if (CollUtils.isEmpty(activityIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<MktActivity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MktActivity::getId, activityIds);
        return BeanUtils.copyToList(baseMapper.selectList(queryWrapper), RemoteActivityVo.class);
    }
}
