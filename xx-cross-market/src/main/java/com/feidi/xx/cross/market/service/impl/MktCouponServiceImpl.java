package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.StatusEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.cache.market.manage.MktCacheManager;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.CouponTemplateStatusEnum;
import com.feidi.xx.cross.common.enums.market.ShelvesStatusEnum;
import com.feidi.xx.cross.common.enums.market.TargetedCouponsStatusEnum;
import com.feidi.xx.cross.common.utils.NameSetterUtil;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.MktCouponGrant;
import com.feidi.xx.cross.market.domain.bo.MktCouponBo;
import com.feidi.xx.cross.market.domain.vo.MktActivityVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.mapper.*;
import com.feidi.xx.cross.market.service.IMktCouponService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@RequiredArgsConstructor
@Service
public class MktCouponServiceImpl implements IMktCouponService {

    private final MktCouponMapper baseMapper;
    private final MktActivityMapper activityMapper;

    private final MktCacheManager mktCacheManager;

    private final MktCouponGrantMapper mktCouponGrantMapper;

    private final MktTargetedCouponsMapper targetedCouponsMapper;

    private final MktPassengerInviteRewardConfigMapper mktPassengerInviteRewardConfigMapper;


    /**
     * 查询优惠券
     *
     * @param id 主键
     * @return 优惠券
     */
    @Override
    public MktCouponVo queryById(Long id) {
        MktCouponVo mktCouponVo = baseMapper.selectVoById(id);
        if (mktCouponVo == null) {
            return null;
        }
        var w1 = Wrappers.<MktCouponGrant>lambdaQuery()
                .select(MktCouponGrant::getId)
                .eq(MktCouponGrant::getCouponId, id)
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.USED.getCode());
        mktCouponVo.setQuantityUsed(mktCouponGrantMapper.selectCount(w1));
        var w2 = Wrappers.<MktCouponGrant>lambdaQuery()
                .select(MktCouponGrant::getId)
                .eq(MktCouponGrant::getCouponId, id)
                .eq(MktCouponGrant::getUsingStatus, CouponStatusEnum.NOT_USED.getCode());
        mktCouponVo.setQuantityAvailable(mktCouponGrantMapper.selectCount(w2));

        NameSetterUtil.cityNameSetterByCode(List.of(mktCouponVo), MktCouponVo::getCityCodeList, MktCouponVo::setCityNames);
        NameSetterUtil.lineNameSetter(List.of(mktCouponVo), MktCouponVo::getLineIdList, MktCouponVo::setLineNames);
        return mktCouponVo;
    }

    @Override
    public List<MktCouponVo> queryCoupon(MktCouponBo bo) {
        LambdaQueryWrapper<MktCoupon> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 分页查询优惠券列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 优惠券分页列表
     */
    @Override
    public TableDataInfo<MktCouponVo> queryPageList(MktCouponBo bo, PageQuery pageQuery) {

        //初始化
        if (ObjectUtils.isEmpty(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }
        if (ObjectUtils.isEmpty(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("create_time");
        }
        LambdaQueryWrapper<MktCoupon> lqw = buildQueryWrapper(bo);
        Page<MktCouponVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        NameSetterUtil.cityNameSetterByCode(result.getRecords(), MktCouponVo::getCityCodeList, MktCouponVo::setCityNames);
        NameSetterUtil.lineNameSetter(result.getRecords(), MktCouponVo::getLineIdList, MktCouponVo::setLineNames);

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的优惠券列表
     *
     * @param bo 查询条件
     * @return 优惠券列表
     */
    @Override
    public List<MktCouponVo> queryList(MktCouponBo bo) {
        LambdaQueryWrapper<MktCoupon> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktCoupon> buildQueryWrapper(MktCouponBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktCoupon> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getActivityId() != null, MktCoupon::getActivityId, bo.getActivityId());
        lqw.eq(bo.getAgentId() != null, MktCoupon::getAgentId, bo.getAgentId());
        lqw.ge(bo.getMargin() != null, MktCoupon::getMargin, bo.getMargin());
        lqw.like(StringUtils.isNotBlank(bo.getName()), MktCoupon::getName, bo.getName());
        lqw.like(StringUtils.isNotBlank(bo.getCityCode()), MktCoupon::getCityCode, bo.getCityCode());
        lqw.like(StringUtils.isNotBlank(bo.getLineId()), MktCoupon::getLineId, bo.getLineId());
        lqw.eq(StringUtils.isNotBlank(bo.getDiscountType()), MktCoupon::getDiscountType, bo.getDiscountType());
        lqw.eq(StringUtils.isNotBlank(bo.getPaidType()), MktCoupon::getPaidType, bo.getPaidType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), MktCoupon::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getTarget()), MktCoupon::getTarget, bo.getTarget());
        lqw.eq(StringUtils.isNotBlank(bo.getShelvesStatus()), MktCoupon::getShelvesStatus, bo.getShelvesStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getProductScope()), MktCoupon::getProductScope, bo.getProductScope());

        lqw.ge(bo.getStartCreateTime() != null, MktCoupon::getCreateTime, bo.getStartCreateTime());
        lqw.le(bo.getEndCreateTime() != null, MktCoupon::getCreateTime, bo.getEndCreateTime());

        if (bo.getOnlyValid() != null && bo.getOnlyValid()) {
            lqw.and(w -> {
                w.gt(MktCoupon::getExpireTime, 0).or().gt(MktCoupon::getEndTime, new Date());
            });
        }
        if (CollUtil.isNotEmpty(bo.getCouponIds())) {
            lqw.in(MktCoupon::getId, bo.getCouponIds());
        }
        return lqw;
    }

    /**
     * 新增优惠券
     *
     * @param bo 优惠券
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktCouponBo bo) {
        MktCoupon add = MapstructUtils.convert(bo, MktCoupon.class);
        MktActivityVo mktActivityVo = activityMapper.selectVoById(add.getActivityId());
        if (ObjectUtils.isNotEmpty(mktActivityVo)) {
            if (mktActivityVo.getStatus().equals(StatusEnum.DISABLE.getCode())) {
                throw new ServiceException("活动已结束");
            }
            add.setActivityName(mktActivityVo.getName());
        }

        validEntityBeforeSave(add);
        add.setMargin(add.getTotal());
        if (ObjectUtils.isEmpty(add.getMaxNum())) {
            add.setMaxNum(1);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            addCache(add);
        }
        return flag;
    }

    private void addCache(MktCoupon add) {
        //根据结束时间获取缓存时间
        MktActivityVo mktActivityVo = activityMapper.selectVoById(add.getActivityId());
        if (ObjectUtils.isNotEmpty(mktActivityVo)) {
            Date endTime = mktActivityVo.getEndTime();
            long now = System.currentTimeMillis();
            long diffSeconds = (endTime.getTime() - now) / 1000;
            mktCacheManager.createCouponStock(add.getId(), add.getMargin(), add.getMaxNum(), diffSeconds);
        } else {
            mktCacheManager.createCouponStock(add.getId(), add.getMargin(), add.getMaxNum());
        }

    }


    public void deleteCache(Long couponId) {
        mktCacheManager.deleteCouponStock(couponId);
    }

    /**
     * 修改优惠券
     *
     * @param bo 优惠券
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktCouponBo bo) {
        MktCoupon update = MapstructUtils.convert(bo, MktCoupon.class);
        validEntityBeforeSave(update);

//        if (bo.getAddNumber() != null && bo.getAddNumber() > 0) {
//            update.setTotal(update.getTotal() + bo.getAddNumber());
//            update.setMargin(update.getMargin() + bo.getAddNumber());
//        }
        update.setMargin(update.getTotal());

        boolean success = baseMapper.updateById(update) > 0;
        if (success) {
            MktCoupon mktCoupon = baseMapper.selectById(update.getId());
            deleteCache(mktCoupon.getId());
            addCache(mktCoupon);
        }
        return success;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktCoupon entity) {
        boolean b = baseMapper.nameSame(entity.getId(), entity.getName());
        if (b) {
            throw new ServiceException("优惠券名称已存在");
        }
        if (entity.getId() != null) {
            MktCoupon mktCoupon = baseMapper.selectById(entity.getId());
            if (mktCoupon.getShelvesStatus().equals(ShelvesStatusEnum.ONLINE.getCode())) {
                // 上架时，有活动（包含定向）关联不能编辑 msg: 优惠卷已绑定活动，不可编辑
                Long i = targetedCouponsMapper.selectCountByCouponId(entity.getId(), TargetedCouponsStatusEnum.ISSUED);
                if (i > 0) {
                    throw new ServiceException("优惠券已绑定定向发券，不可操作！");
                }
                if (activityMapper.selectCountByCouponId(entity.getId()) > 0) {
                    throw new ServiceException("优惠券已绑定活动，不可操作！");
                }
                if (mktPassengerInviteRewardConfigMapper.selectCountByCouponId(entity.getId()) > 0) {
                    throw new ServiceException("优惠券已绑定乘客推乘客活动，不可操作！");
                }
            }
        }
        //同一个适用范围的同类型产品不能在同一时间创建两个一模一样的优惠券
        var w = Wrappers.<MktCoupon>lambdaQuery()
                .ne(entity.getId() != null, MktCoupon::getId, entity.getId())
                .eq(MktCoupon::getTarget, entity.getTarget())
                .eq(MktCoupon::getProductScope, entity.getProductScope())
                .eq(MktCoupon::getStartTime, entity.getStartTime())
                .eq(MktCoupon::getEndTime, entity.getEndTime());
        if (baseMapper.selectCount(w) > 0) {
            throw new ServiceException("同一个适用范围的同类型产品不能在同一时间创建优惠券！");
        }
    }

    /**
     * 校验并批量删除优惠券信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            //判断已上架的不能删除
            LambdaQueryWrapper<MktCoupon> lqw = new LambdaQueryWrapper<>();
            lqw.eq(MktCoupon::getShelvesStatus, ShelvesStatusEnum.ONLINE.getCode());
            lqw.in(MktCoupon::getId, ids);
            Long l = baseMapper.selectCount(lqw);
            if (l > 0) {
                throw new ServiceException("已上架的优惠券不能删除");
            }
            for (Long id : ids) {
                Long i = targetedCouponsMapper.selectCountByCouponId(id, TargetedCouponsStatusEnum.ISSUED);
                if (i > 0) {
                    throw new ServiceException("优惠券已绑定定向发券，不可删除！");
                }
                if (activityMapper.selectCountByCouponId(id) > 0) {
                    throw new ServiceException("优惠券已绑定活动，不可操作！");
                }
                if (mktPassengerInviteRewardConfigMapper.selectCountByCouponId(id) > 0) {
                    throw new ServiceException("优惠券已绑定乘客推乘客活动，不可操作！");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 验证优惠券是否可用
     *
     * @param couponId 优惠券id
     * @return
     */
    @Override
    public MktCouponVo verifyCoupon(Long couponId) {

        MktCouponVo mktCouponVo = queryById(couponId);

        if (mktCouponVo == null) {
            throw new ServiceException("优惠券不存在");
        }

        // TODO 校验优惠券是否可用
        if (mktCouponVo.getStatus().equals(CouponTemplateStatusEnum.ENDED.getCode())) {
            throw new ServiceException("优惠券已失效");
        }
        return mktCouponVo;
    }

    @Override
    public Map<Long, MktCouponVo> queryMapByIds(Set<Long> longs) {
        return queryMapByIds(longs, false);
    }

    @Override
    public Map<Long, MktCouponVo> queryMapByIds(Set<Long> longs, Boolean queryAssociatedInfo) {
        if (CollUtils.isEmpty(longs)) {
            return Map.of();
        }
        List<MktCouponVo> mktCoupons = baseMapper.selectVoBatchIds(longs);
        if (BooleanUtils.isTrue(queryAssociatedInfo)) {
            NameSetterUtil.cityNameSetterByCode(mktCoupons, MktCouponVo::getCityCodeList, MktCouponVo::setCityNames);
            NameSetterUtil.lineNameSetter(mktCoupons, MktCouponVo::getLineIdList, MktCouponVo::setLineNames);
        }
        return mktCoupons.stream().collect(Collectors.toMap(MktCouponVo::getId, v -> v));
    }

    @Override
    public List<MktCouponVo> queryListByIds(Set<Long> couponIds) {
        if (CollUtils.isNotEmpty(couponIds)) {
            return baseMapper.selectVoBatchIds(couponIds);
        }
        return List.of();
    }
}
