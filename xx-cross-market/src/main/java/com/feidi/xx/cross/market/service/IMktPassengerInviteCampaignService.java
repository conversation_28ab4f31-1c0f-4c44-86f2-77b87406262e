package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;

import java.util.Collection;
import java.util.List;

/**
 * 乘客推乘客活动主Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IMktPassengerInviteCampaignService {

    /**
     * 查询乘客推乘客活动主
     *
     * @param id 主键
     * @return 乘客推乘客活动主
     */
    MktPassengerInviteCampaignVo queryById(Long id);

    /**
     * 分页查询乘客推乘客活动主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客活动主分页列表
     */
    TableDataInfo<MktPassengerInviteCampaignVo> queryPageList(MktPassengerInviteCampaignBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客推乘客活动主列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客活动主列表
     */
    List<MktPassengerInviteCampaignVo> queryList(MktPassengerInviteCampaignBo bo);

    /**
     * 新增乘客推乘客活动主
     *
     * @param bo 乘客推乘客活动主
     * @return 是否新增成功
     */
    Boolean insertByBo(MktPassengerInviteCampaignBo bo);

    /**
     * 修改乘客推乘客活动主
     *
     * @param bo 乘客推乘客活动主
     * @return 是否修改成功
     */
    Boolean updateByBo(MktPassengerInviteCampaignBo bo);

    /**
     * 校验并批量删除乘客推乘客活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新乘客推乘客活动状态
     *
     * @param bo 包含id和status的业务对象
     */
    void updateStatus(MktPassengerInviteCampaignBo bo);

    /**
     * 定时更新乘客推乘客活动状态
     */
    void updateStatusTask();

    /**
     * 根据邀请码查询活动
     *
     * @param inviteCode 邀请码
     * @return 活动
     */
    MktPassengerInviteCampaignVo queryByInviteCode(String inviteCode);
}
