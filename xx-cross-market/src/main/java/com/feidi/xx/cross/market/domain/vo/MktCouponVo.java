package com.feidi.xx.cross.market.domain.vo;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.common.excel.annotation.ExcelDictFormat;
import com.feidi.xx.common.excel.convert.ExcelDictConvert;
import com.feidi.xx.cross.common.enums.market.*;
import com.feidi.xx.cross.market.domain.MktCoupon;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 优惠券视图对象 mkt_coupon
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktCoupon.class)
public class MktCouponVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 线路id
     */
    private String lineId;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String name;

    /**
     * 折扣方式
     */
    @ExcelProperty(value = "折扣方式")
    @Enum2Text(enumClass = DiscountTypeEnum.class)
    private String discountType;

    /**
     * 额度
     */
    @ExcelProperty(value = "额度")
    private Long quota;

    /**
     * 总数
     */
    @ExcelProperty(value = "总数")
    private Integer total;

    /**
     * 余量
     */
    @ExcelProperty(value = "余量")
    private Integer margin;


    /**
     * 优惠规则
     */
    @ExcelProperty(value = "优惠规则")
    private String rule;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Long expireTime;

    /**
     * 使用条件 满 x 元可用 满减卷
     */
    @ExcelProperty(value = "使用条件")
    private String termsOfUse;

    /**
     * 领取方式
     */
    @ExcelProperty(value = "领取方式")
    @Enum2Text(enumClass = PaidTypeEnum.class)
    private String paidType;

    /**
     * 最大领取数量
     */
    @ExcelProperty(value = "最大领取数量")
    private Integer maxNum;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "CouponTemplateStatusEnum")
    @Enum2Text(enumClass = CouponTemplateStatusEnum.class)
    private String status;

    /**
     * 优惠对象[CouponTargetEnum]
     */
    @ExcelProperty(value = "优惠对象")
    @Enum2Text(enumClass = CouponTargetEnum.class)
    private String target;

    /**
     * 优惠券来源 [CouponSourceEnum]
     */
    @ExcelProperty(value = "优惠券来源")
    @Enum2Text(enumClass = CouponSourceEnum.class)
    private String source;

    /**
     * 上下架状态：0=下架，1=上架
     */
    @ExcelProperty(value = "上下架状态")
    @Enum2Text(enumClass = ShelvesStatusEnum.class)
    private String shelvesStatus;

    /**
     * 生效开始时间
     */
    @ExcelProperty(value = "生效开始时间")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @ExcelProperty(value = "生效结束时间")
    private Date endTime;

    /**
     * 产品范围：0=全部，1=独享，2=拼车
     */
    @ExcelProperty(value = "产品范围")
    @Enum2Text(enumClass = ProductScopeEnum.class)
    private String productScope;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 使用数量
     */
    private Long quantityUsed;

    /**
     * 待使用数
     */
    private Long quantityAvailable;

    public List<String> getCityCodeList() {
        if (StrUtil.isNotBlank(cityCode)) {
            return StrUtil.split(cityCode, CharPool.COMMA).stream().filter(StrUtil::isNotBlank).toList();
        }
        return List.of();
    }

    public List<Long> getLineIdList() {
        if (StrUtil.isNotBlank(lineId)) {
            return StrUtil.split(lineId, CharPool.COMMA).stream().filter(StrUtil::isNotBlank).map(NumberUtil::parseLong).toList();
        }
        return List.of();
    }

    /**
     * 城市名称列表
     */
    private List<String> cityNames;

    /**
     * 线路名称列表
     */
    private List<String> lineNames;

    /**
     * 获取优惠券优惠内容
     */
    public String getCouponContent() {
        DiscountTypeEnum discountType = DiscountTypeEnum.getByCode(this.discountType);
        if (discountType == DiscountTypeEnum.DEDUCTION) {
            return StrUtil.format("立减{}元", NumberUtil.div(BigDecimal.valueOf(quota), 100));
        }
        if (discountType == DiscountTypeEnum.DISCOUNT) {
            return StrUtil.format("满{}元可用，打{}折", termsOfUse, NumberUtil.div(BigDecimal.valueOf(quota), 100));
        }
        return "";
    }

}
