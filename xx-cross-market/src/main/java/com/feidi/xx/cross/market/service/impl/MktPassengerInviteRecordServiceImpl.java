package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 乘客推乘客邀请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRecordServiceImpl implements IMktPassengerInviteRecordService {

    private final MktPassengerInviteRecordMapper baseMapper;

    private final IMktCouponService mktCouponService;

    /**
     * 查询乘客推乘客邀请记录
     *
     * @param id 主键
     * @return 乘客推乘客邀请记录
     */
    @Override
    public MktPassengerInviteRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客邀请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客邀请记录分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRecordVo> queryPageList(MktPassengerInviteRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客邀请记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客邀请记录列表
     */
    @Override
    public List<MktPassengerInviteRecordVo> queryList(MktPassengerInviteRecordBo bo) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteRecord> buildQueryWrapper(MktPassengerInviteRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteRecord::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getInviterId() != null, MktPassengerInviteRecord::getInviterId, bo.getInviterId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviterMobile()), MktPassengerInviteRecord::getInviterMobile, bo.getInviterMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getInviterCityCode()), MktPassengerInviteRecord::getInviterCityCode, bo.getInviterCityCode());
        lqw.eq(bo.getInviteeId() != null, MktPassengerInviteRecord::getInviteeId, bo.getInviteeId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteeMobile()), MktPassengerInviteRecord::getInviteeMobile, bo.getInviteeMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteeCityCode()), MktPassengerInviteRecord::getInviteeCityCode, bo.getInviteeCityCode());
        lqw.eq(bo.getInviteTime() != null, MktPassengerInviteRecord::getInviteTime, bo.getInviteTime());
        lqw.eq(bo.getOrderCompleteTime() != null, MktPassengerInviteRecord::getOrderCompleteTime, bo.getOrderCompleteTime());
        lqw.eq(bo.getOrderId() != null, MktPassengerInviteRecord::getOrderId, bo.getOrderId());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteRecord::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 根据活动id查询邀请统计
     *
     * @param campaignIds 活动id集合
     * @return 邀请统计列表
     */
    @Override
    public Map<Long, InviteStatisticsVo> queryInviteStatisticsByCampaignIds(Collection<Long> campaignIds) {
        List<InviteStatisticsVo> list = baseMapper.selectInviteStatisticsByCampaignIds(campaignIds);
        Map<Long, InviteStatisticsVo> map = new HashMap<>();
        for (Long campaignId : campaignIds) {
            //如果活动不存在的则返回0
            list.stream().filter(item -> item.getCampaignId().equals(campaignId)).findFirst()
                    .ifPresentOrElse(item -> map.put(campaignId, item), () -> map.put(campaignId, new InviteStatisticsVo(campaignId)));
        }
        return map;
    }

    /**
     * 分页查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邀请记录详情分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRecordDetailVo> queryInviteRecordDetailPageList(MktPassengerInviteRecordDetailQueryBo bo, PageQuery pageQuery) {
        Page<MktPassengerInviteRecordDetailVo> page = pageQuery.build();
        if (bo.getConditionType() != null && bo.getConditionType().equals(PassengerInviteConditionTypeEnum.REGISTER.getCode())) {
            page.addOrder(OrderItem.desc("invite_time"));
        } else if (bo.getConditionType() != null && bo.getConditionType().equals(PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode())) {
            page.addOrder(OrderItem.desc("order_complete_time"));
        } else {
            page.addOrder(OrderItem.desc("id"));
        }
        baseMapper.selectInviteRecordDetailPage(page, bo);
        // 设置优惠券信息
        formatData(page.getRecords());
        return TableDataInfo.build(page);
    }

    /**
     * 查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo 查询条件
     * @return 邀请记录详情列表
     */
    @Override
    public List<MktPassengerInviteRecordDetailVo> queryInviteRecordDetailList(MktPassengerInviteRecordDetailQueryBo bo) {
        Page<MktPassengerInviteRecordDetailVo> page = baseMapper.selectInviteRecordDetailPage(bo.build(), bo);
        // 设置优惠券信息
        formatData(page.getRecords());
        return page.getRecords();
    }

    public void formatData(List<MktPassengerInviteRecordDetailVo> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(MktPassengerInviteRecordDetailVo::setRefundRewardOrRewardTime);
        Set<Long> couponIds = list.stream().filter(item -> item.getRewardType().equals(PassengerInviteRewardTypeEnum.COUPON.getCode()))
                .map(e -> e.getRewardMeta().getCouponIds())
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        if (CollUtil.isEmpty(couponIds)) {
            return;
        }
        Map<Long, MktCouponVo> couponVoMap = mktCouponService.queryMapByIds(couponIds);
        // 设置优惠券信息
        list.forEach(item -> {
            if (item.getRewardType().equals(PassengerInviteRewardTypeEnum.COUPON.getCode())) {
                List<MktCouponVo> mktCouponVos = item.getRewardMeta().getCouponIds().stream()
                        .map(couponVoMap::get)
                        .filter(Objects::nonNull).toList();
                item.setRewardContent(mktCouponVos.stream().map(MktCouponVo::getCouponContent).collect(Collectors.joining("，")));
                item.getRewardMeta().setCouponVos(mktCouponVos);
            }
        });
    }

    /**
     * 新增乘客推乘客邀请记录
     *
     * @param bo 乘客推乘客邀请记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteRecordBo bo) {
        MktPassengerInviteRecord add = MapstructUtils.convert(bo, MktPassengerInviteRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客邀请记录
     *
     * @param bo 乘客推乘客邀请记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteRecordBo bo) {
        MktPassengerInviteRecord update = MapstructUtils.convert(bo, MktPassengerInviteRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 判断乘客是否参与过邀请活动
     *
     * @param passengerId 乘客id
     * @return true：已参与 false：未参与
     */
    @Override
    public boolean isJoin(Long passengerId) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(MktPassengerInviteRecord::getInviteeId, passengerId);
        return baseMapper.selectCount(lqw) > 0;
    }

    /**
     * 根据被邀请人ID查询邀请记录
     *
     * @param inviteeId 被邀请人ID
     * @return 邀请记录
     */
    @Override
    public MktPassengerInviteRecordVo queryByInviteeId(Long inviteeId) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(MktPassengerInviteRecord::getInviteeId, inviteeId);
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 更新邀请记录状态为已首单
     *
     * @param id                邀请记录ID
     * @param orderId           订单ID
     * @param orderCompleteTime 订单完成时间
     * @return 是否更新成功
     */
    @Override
    public Boolean updateToFirstOrder(Long id, Long orderId, Date orderCompleteTime) {
        LambdaUpdateWrapper<MktPassengerInviteRecord> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper
                .eq(MktPassengerInviteRecord::getId, id)
                .set(MktPassengerInviteRecord::getStatus, PassengerInviteStatusEnum.FIRST_ORDER.getCode())
                .set(MktPassengerInviteRecord::getOrderId, orderId)
                .set(MktPassengerInviteRecord::getOrderCompleteTime, orderCompleteTime);
        return baseMapper.update(updateWrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteRecord entity) {
    }
}
