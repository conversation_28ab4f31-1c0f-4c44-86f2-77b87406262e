package com.feidi.xx.cross.market.config;

import com.feidi.xx.cross.market.service.reward.RewardGrantStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategyContainer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 奖励策略配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class RewardStrategyConfig {

    private final List<RewardGrantStrategy> rewardStrategies;

    /**
     * 创建策略映射Bean
     *
     * @return 策略映射
     */
    @Bean
    public RewardGrantStrategyContainer rewardStrategyMap() {
        Map<String, RewardGrantStrategy> strategyMap = new HashMap<>();

        for (RewardGrantStrategy strategy : rewardStrategies) {
            strategyMap.put(strategy.getRewardType(), strategy);
            log.info("注册奖励策略：{} -> {}", strategy.getRewardType(), strategy.getClass().getSimpleName());
        }
        log.info("奖励策略映射初始化完成，共注册{}个策略", strategyMap.size());
        return new RewardGrantStrategyContainer(strategyMap);
    }
}
