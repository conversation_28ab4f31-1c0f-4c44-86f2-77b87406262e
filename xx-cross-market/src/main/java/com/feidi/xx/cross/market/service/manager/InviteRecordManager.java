package com.feidi.xx.cross.market.service.manager;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.result.RewardRevokeResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardService;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategyContainer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 邀请记录管理器
 * 负责邀请记录的创建、更新、查询，处理首单奖励逻辑和奖励撤销逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InviteRecordManager {

    private final IMktPassengerInviteRecordService inviteRecordService;
    private final IMktPassengerInviteRewardService inviteRewardService;
    private final RewardGrantManager rewardGrantManager;
    private final RewardGrantStrategyContainer strategyContainer;

    /**
     * 创建邀请记录
     *
     * @param recordBo 邀请记录业务对象
     * @return 创建的邀请记录ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createInviteRecord(MktPassengerInviteRecordBo recordBo) {
        try {
            log.info("开始创建邀请记录，邀请人ID：{}，被邀请人ID：{}", recordBo.getInviterId(), recordBo.getInviteeId());

            // 设置初始状态为已注册
            recordBo.setStatus(PassengerInviteStatusEnum.REGISTERED.getCode());
            recordBo.setInviteTime(new Date());

            // 创建邀请记录
            Boolean created = inviteRecordService.insertByBo(recordBo);
            if (!created) {
                throw new ServiceException("创建邀请记录失败");
            }

            log.info("邀请记录创建成功，记录ID：{}", recordBo.getId());
            return recordBo.getId();

        } catch (Exception e) {
            log.error("创建邀请记录失败，邀请人ID：{}，被邀请人ID：{}", recordBo.getInviterId(), recordBo.getInviteeId(), e);
            throw new ServiceException("创建邀请记录失败：" + e.getMessage());
        }
    }

    /**
     * 处理注册奖励发放
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs  奖励配置列表
     * @param inviterId      邀请人ID
     * @param inviteeId      被邀请人ID
     * @return 发放是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean handleRegistrationRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs,
                                             Long inviterId, Long inviteeId) {
        try {
            log.info("开始处理注册奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);

            // 委托给奖励发放管理器处理
            boolean success = rewardGrantManager.grantRegistrationRewards(inviteRecordId, rewardConfigs, inviterId, inviteeId);

            if (success) {
                log.info("注册奖励处理成功，邀请记录ID：{}", inviteRecordId);
            } else {
                log.warn("注册奖励处理部分失败，邀请记录ID：{}", inviteRecordId);
            }

            return success;

        } catch (Exception e) {
            log.error("处理注册奖励失败，邀请记录ID：{}", inviteRecordId, e);
            throw new ServiceException("处理注册奖励失败：" + e.getMessage());
        }
    }

    /**
     * 更新邀请记录为首单状态
     *
     * @param inviteRecordId    邀请记录ID
     * @param orderId           订单ID
     * @param orderCompleteTime 订单完成时间
     * @return 更新是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateToFirstOrderStatus(Long inviteRecordId, Long orderId, Date orderCompleteTime) {
        try {
            log.info("开始更新邀请记录为首单状态，记录ID：{}，订单ID：{}", inviteRecordId, orderId);

            Boolean updated = inviteRecordService.updateToFirstOrder(inviteRecordId, orderId, orderCompleteTime);
            if (!updated) {
                throw new ServiceException("更新邀请记录状态失败");
            }

            log.info("邀请记录状态更新成功，记录ID：{}", inviteRecordId);
            return true;

        } catch (Exception e) {
            log.error("更新邀请记录状态失败，记录ID：{}，订单ID：{}", inviteRecordId, orderId, e);
            throw new ServiceException("更新邀请记录状态失败：" + e.getMessage());
        }
    }

    /**
     * 处理首单奖励发放
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs  奖励配置列表
     * @param inviterId      邀请人ID
     * @param inviteeId      被邀请人ID
     * @return 发放是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean handleFirstOrderRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs,
                                           Long inviterId, Long inviteeId) {
        try {
            log.info("开始处理首单奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);

            // 委托给奖励发放管理器处理
            boolean success = rewardGrantManager.grantFirstOrderRewards(inviteRecordId, rewardConfigs, inviterId, inviteeId);

            if (success) {
                log.info("首单奖励处理成功，邀请记录ID：{}", inviteRecordId);
            } else {
                log.warn("首单奖励处理部分失败，邀请记录ID：{}", inviteRecordId);
            }

            return success;

        } catch (Exception e) {
            log.error("处理首单奖励失败，邀请记录ID：{}", inviteRecordId, e);
            throw new ServiceException("处理首单奖励失败：" + e.getMessage());
        }
    }

    /**
     * 撤销奖励（客诉时使用）
     *
     * @param inviteRecordId 邀请记录ID
     * @return 撤销是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean revokeRewards(Long inviteRecordId) {
        try {
            log.info("开始撤销奖励，邀请记录ID：{}", inviteRecordId);

            // 查询该邀请记录下的所有已发放奖励
            List<MktPassengerInviteRewardVo> rewards = inviteRewardService.queryByInviteRecordId(inviteRecordId);
            if (CollUtil.isEmpty(rewards)) {
                log.warn("查询到邀请记录下没有奖励，邀请记录ID：{}", inviteRecordId);
                return true;
            }

            boolean allSuccess = true;

            for (MktPassengerInviteRewardVo reward : rewards) {
                // 只撤销首单奖励
                if (!PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(reward.getConditionType())) {
                    log.debug("非首单奖励，跳过撤销，奖励ID：{}", reward.getId());
                    continue;
                }
                // 只撤销已发放的首单奖励
                if (!PassengerInviteRewardStatusEnum.GRANTED.getCode().equals(reward.getStatus())) {
                    continue;
                }

                boolean revokeSuccess = revokeSingleReward(reward);
                if (!revokeSuccess) {
                    allSuccess = false;
                }
            }

            log.info("奖励撤销完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
            return allSuccess;

        } catch (Exception e) {
            log.error("撤销奖励失败，邀请记录ID：{}", inviteRecordId, e);
            throw new ServiceException("撤销奖励失败：" + e.getMessage());
        }
    }

    /**
     * 撤销单个奖励
     *
     * @param reward 奖励记录
     * @return 撤销是否成功
     */
    private boolean revokeSingleReward(MktPassengerInviteRewardVo reward) {
        try {
            log.info("开始撤销单个奖励，奖励ID：{}，类型：{}", reward.getId(), reward.getRewardType());

            // 获取对应的策略
            RewardGrantStrategy strategy = strategyContainer.getStrategy(reward.getRewardType());
            if (strategy == null) {
                log.error("未找到对应的奖励撤销策略，奖励类型：{}", reward.getRewardType());
                updateRewardRevokeStatus(reward.getId(), false, "未找到对应的奖励撤销策略");
                return false;
            }

            // 执行奖励撤销
            RewardRevokeResult result = strategy.revokeReward(reward);

            // 更新奖励状态
            boolean updateSuccess = updateRewardRevokeStatus(reward.getId(), result.isSuccess(), result.getRemark());

            if (result.isSuccess() && updateSuccess) {
                log.info("撤销单个奖励成功，奖励ID：{}", reward.getId());
                return true;
            } else {
                log.error("撤销单个奖励失败，奖励ID：{}，撤销结果：{}，更新结果：{}",
                        reward.getId(), result.isSuccess(), updateSuccess);
                return false;
            }

        } catch (Exception e) {
            log.error("撤销单个奖励时发生异常，奖励ID：{}", reward.getId(), e);
            return false;
        }
    }

    /**
     * 更新奖励撤销状态
     *
     * @param rewardId      奖励记录ID
     * @param revokeSuccess 撤销是否成功
     * @param remark        备注信息
     * @return 更新是否成功
     */
    private boolean updateRewardRevokeStatus(Long rewardId, boolean revokeSuccess, String remark) {
        try {
            log.debug("更新奖励撤销状态，记录ID：{}，撤销结果：{}", rewardId, revokeSuccess);

            MktPassengerInviteRewardBo updateBo = new MktPassengerInviteRewardBo();
            updateBo.setId(rewardId);
            updateBo.setStatus(revokeSuccess ? PassengerInviteRewardStatusEnum.REFUND.getCode()
                    : PassengerInviteRewardStatusEnum.REFUND_FAILED.getCode());
            updateBo.setRefundRewardTime(new Date());
            updateBo.setRemark(remark != null ? remark : "客诉撤销奖励");

            Boolean updated = inviteRewardService.updateByBo(updateBo);
            if (updated) {
                log.debug("奖励撤销状态更新成功，记录ID：{}", rewardId);
                return true;
            } else {
                log.error("奖励撤销状态更新失败，记录ID：{}", rewardId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新奖励撤销状态异常，记录ID：{}", rewardId, e);
            return false;
        }
    }

    /**
     * 根据被邀请人ID查询邀请记录
     *
     * @param inviteeId 被邀请人ID
     * @return 邀请记录
     */
    public MktPassengerInviteRecordVo queryByInviteeId(Long inviteeId) {
        return inviteRecordService.queryByInviteeId(inviteeId);
    }
}
