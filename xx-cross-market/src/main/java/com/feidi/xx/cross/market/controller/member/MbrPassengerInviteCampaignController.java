package com.feidi.xx.cross.market.controller.member;

import cn.hutool.core.util.StrUtil;
import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.GetPassengerInviteCampaignShareBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignMbrVo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.bo.PassengerInviteCampaignMbrQueryBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.domain.vo.ShareInfoVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import com.feidi.xx.cross.market.service.MktPassengerInviteCampaignMbrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 乘客端-乘客推乘客活动
 * 前端访问路由地址为:/market/br/passengerInviteCampaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/passengerInviteCampaign")
public class MbrPassengerInviteCampaignController extends BaseController {

    private final MktPassengerInviteCampaignMbrService mktPassengerInviteCampaignMbrService;

    private final IMktPassengerInviteRecordService mktPassengerInviteRecordService;

    /**
     * 根据城市code和邀请码查询活动
     * 可以不登录访问
     *
     * @param queryBo 查询参数
     * @return 活动信息
     */
    @Enum2TextAspect
    @GetMapping("/queryByLocationAndInviteCode")
    public R<MktPassengerInviteCampaignMbrVo> queryByLocationAndInviteCode(PassengerInviteCampaignMbrQueryBo queryBo) {
        if (StrUtil.isBlank(queryBo.getInviteCode()) && StrUtil.isBlank(queryBo.getCityCode())) {
            log.warn("查询活动但参数都为空, queryByLocationAndInviteCode, queryBo: {}", queryBo);
            return R.ok(null);
        }
        return R.ok(mktPassengerInviteCampaignMbrService.queryByLocationAndInviteCode(queryBo));
    }

    /**
     * 查询邀请数据记录
     */
    @GetMapping("/queryInviteRecord")
    public TableDataInfo<MktPassengerInviteRecordDetailVo> queryInviteRecord(MktPassengerInviteRecordDetailQueryBo queryBo, PageQuery pageQuery) {
        Assert.notNull(queryBo.getCampaignId(), "活动id不能为空");
        queryBo.setInviterId(LoginHelper.getUserId());
        return mktPassengerInviteRecordService.queryInviteRecordDetailPageList(queryBo, pageQuery);
    }

    /**
     * 获取分享信息分享调用
     */
    @GetMapping("/getShareInfo")
    public R<ShareInfoVo> getShareInfo(@Validated GetPassengerInviteCampaignShareBo bo) {
        return R.ok(mktPassengerInviteCampaignMbrService.getShareInfo(bo));
    }
}
