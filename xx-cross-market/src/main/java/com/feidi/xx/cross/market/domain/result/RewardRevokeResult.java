package com.feidi.xx.cross.market.domain.result;

import lombok.Data;

/**
 * 奖励撤销结果类
 * 
 * <AUTHOR>
 */
@Data
public class RewardRevokeResult {
    
    /**
     * 撤销是否成功
     */
    private boolean success;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 私有构造函数
     */
    private RewardRevokeResult() {
    }
    
    /**
     * 私有构造函数
     * 
     * @param success 是否成功
     * @param errorMessage 错误消息
     * @param remark 备注信息
     */
    private RewardRevokeResult(boolean success, String errorMessage, String remark) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.remark = remark;
    }
    
    /**
     * 创建成功的撤销结果
     * 
     * @return 成功的撤销结果
     */
    public static RewardRevokeResult success() {
        return new RewardRevokeResult(true, null, null);
    }
    
    /**
     * 创建成功的撤销结果并携带备注
     * 
     * @param remark 备注信息
     * @return 成功的撤销结果
     */
    public static RewardRevokeResult success(String remark) {
        return new RewardRevokeResult(true, null, remark);
    }
    
    /**
     * 创建失败的撤销结果
     * 
     * @param errorMessage 错误消息
     * @return 失败的撤销结果
     */
    public static RewardRevokeResult fail(String errorMessage) {
        return new RewardRevokeResult(false, errorMessage, null);
    }
    
    /**
     * 创建失败的撤销结果并携带备注
     * 
     * @param errorMessage 错误消息
     * @param remark 备注信息
     * @return 失败的撤销结果
     */
    public static RewardRevokeResult fail(String errorMessage, String remark) {
        return new RewardRevokeResult(false, errorMessage, remark);
    }
    
    /**
     * 判断撤销是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return !success;
    }
}
