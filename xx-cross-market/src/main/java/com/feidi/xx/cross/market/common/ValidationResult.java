package com.feidi.xx.cross.market.common;

import lombok.Data;

/**
 * 通用验证结果类
 *
 * @param <T> 携带的数据类型
 * <AUTHOR>
 */
@Data
public class ValidationResult<T> {

    /**
     * 验证是否成功
     */
    private boolean success;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 携带的数据
     */
    private T data;

    /**
     * 私有构造函数
     */
    private ValidationResult() {
    }

    /**
     * 私有构造函数
     *
     * @param success      是否成功
     * @param errorMessage 错误消息
     * @param data         携带的数据
     */
    private ValidationResult(boolean success, String errorMessage, T data) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.data = data;
    }

    /**
     * 创建成功的验证结果
     *
     * @param <T> 数据类型
     * @return 成功的验证结果
     */
    public static <T> ValidationResult<T> success() {
        return new ValidationResult<>(true, null, null);
    }

    /**
     * 创建成功的验证结果并携带数据
     *
     * @param data 携带的数据
     * @param <T>  数据类型
     * @return 成功的验证结果
     */
    public static <T> ValidationResult<T> success(T data) {
        return new ValidationResult<>(true, null, data);
    }

    /**
     * 创建失败的验证结果
     *
     * @param errorMessage 错误消息
     * @param <T>          数据类型
     * @return 失败的验证结果
     */
    public static <T> ValidationResult<T> fail(String errorMessage) {
        return new ValidationResult<>(false, errorMessage, null);
    }

    /**
     * 创建失败的验证结果并携带数据
     *
     * @param errorMessage 错误消息
     * @param data         携带的数据
     * @param <T>          数据类型
     * @return 失败的验证结果
     */
    public static <T> ValidationResult<T> fail(String errorMessage, T data) {
        return new ValidationResult<>(false, errorMessage, data);
    }

    /**
     * 设置数据（链式调用）
     *
     * @param data 要设置的数据
     * @return 当前验证结果对象
     */
    public ValidationResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    /**
     * 判断验证是否失败
     *
     * @return 是否失败
     */
    public boolean isFailed() {
        return !success;
    }
}
