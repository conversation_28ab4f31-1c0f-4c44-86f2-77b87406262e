package com.feidi.xx.cross.market.domain.bo;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.*;

/**
 * 优惠券发放参数
 *
 * <AUTHOR>
 */
@Data
public class CouponIssuanceParam {
    /**
     * 放券标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;

    //补全参数，考虑传参还是，自己查，考虑到需要校验单人领取限制。
    //Key couponId value 个数
    @NotEmpty(message = "优惠券不能为空")
    @Max(value = 10, message = "最多只能选择10张优惠券")
    private Map<Long, Integer> couponIds;

    /**
     * 手机号列表
     */
    @Max(value = 100, message = "最多只能选择100个手机号")
    private Set<String> passengerPhones = new HashSet<>();

    /**
     * 上传的数据key
     */
    private String dataKey;

}
