package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.api.domain.RemoteActivityBo;
import com.feidi.xx.cross.market.domain.bo.CouponGrantToUserBo;
import com.feidi.xx.cross.market.domain.bo.MktCouponGrantBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponGrantVo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;

import java.util.Collection;
import java.util.List;

/**
 * 优惠券发放Service接口
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
public interface IMktCouponGrantService {

    /**
     * 查询优惠券发放
     *
     * @param id 主键
     * @return 优惠券发放
     */
    MktCouponGrantVo queryById(Long id);

    /**
     * 更新优惠券状态
     *
     * @param bo 优惠券发放
     * @return 优惠券发放集合
     */
    void updateStatus(MktCouponGrantBo bo);


    /**
     * 根据ids批量获取优惠券发放信息
     */
    List<MktCouponGrantVo> queryByIds(Collection<Long> ids);

    /**
     * 分页查询优惠券发放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 优惠券发放分页列表
     */
    TableDataInfo<MktCouponGrantVo> queryPageList(MktCouponGrantBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的优惠券发放列表
     *
     * @param bo 查询条件
     * @return 优惠券发放列表
     */
    List<MktCouponGrantVo> queryList(MktCouponGrantBo bo);

    /**
     * 新增优惠券发放
     *
     * @param bo 优惠券发放
     * @return 是否新增成功
     */
    void insertByBo(MktCouponGrantBo bo, List<Long> userIds);


    /**
     * 修改优惠券发放
     *
     * @param bo 优惠券发放
     * @return 是否修改成功
     */
    Boolean updateByBo(MktCouponGrantBo bo);

    /**
     * 校验并批量删除优惠券发放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    void grantCoupon(RemoteActivityBo remoteActivityBo, MktCouponVo mktCouponVo);

    /**
     * 领取优惠券
     *
     * @param couponGrantBo 优惠券信息
     */
    void grantCoupon(MktCouponGrantBo couponGrantBo);

    /**
     * 获取当前城市下优惠券信息
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @return 优惠券信息
     */
    List<MktCouponVo> queryCouponList(String longitude, String latitude);

    /**
     * 优惠券过期
     */
    void expiredCoupon();

    /**
     *  发放优惠券
     */
    CouponGrantToUserBo grantCoupon(CouponGrantToUserBo couponGrantToUserBo);

    /**
     * 撤销优惠券
     *
     * @param couponGrantId 优惠券发放ID
     * @return 是否撤销成功
     */
    boolean revokeCoupon(Long couponGrantId);
}
