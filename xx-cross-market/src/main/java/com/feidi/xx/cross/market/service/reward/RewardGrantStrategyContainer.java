package com.feidi.xx.cross.market.service.reward;


import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;

import java.util.Map;

/**
 * 奖励发放策略容器
 *
 * <AUTHOR>
 */
public record RewardGrantStrategyContainer(Map<String, RewardGrantStrategy> strategyMap) {

    /**
     * 获取奖励发放策略
     *
     * @param strategyType 奖励类型
     * @return 奖励发放策略
     * @see PassengerInviteRewardTypeEnum
     */
    public RewardGrantStrategy getStrategy(String strategyType) {
        return strategyMap.get(strategyType);
    }

}
