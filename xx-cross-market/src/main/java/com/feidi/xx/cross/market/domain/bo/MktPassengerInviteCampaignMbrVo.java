package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@Data
public class MktPassengerInviteCampaignMbrVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 活动状态 0:待开始 1:进行中 2:已结束
     */
    @Enum2Text(enumClass = PassengerInviteCampaignStatusEnum.class)
    private String status;

    /**
     * 活动规则
     */
    private String ruleContent;

    /**
     * 单个用户最多可邀请人数(0表示不限)
     */
    private Long maxInvites;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 首页文案
     */
    private String entranceText;

    /**
     * 城市code数组
     */
    private List<String> cityCode;

    /**
     * 奖励配置列表
     */
    private List<MktPassengerInviteRewardConfigVo> rewardConfigList = new ArrayList<>();

    public void setRewardConfigList(List<MktPassengerInviteRewardConfigVo> rewardConfigList) {
        this.rewardConfigList = rewardConfigList;
        if (rewardConfigList != null) {
            this.couponStock = rewardConfigList.stream()
                    .filter(e -> e.getRewardType().equals(PassengerInviteRewardTypeEnum.COUPON.getCode()))
                    .map(MktPassengerInviteRewardConfigVo::getCouponVos)
                    .flatMap(Collection::stream)
                    .mapToInt(MktCouponVo::getMargin).sum();
        }
    }

    /**
     * 优惠券余量
     */
    private Integer couponStock = 0;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 邀请人乘客ID；通过别人邀请码进入活动页时返回
     */
    private Long inviterId;
}
