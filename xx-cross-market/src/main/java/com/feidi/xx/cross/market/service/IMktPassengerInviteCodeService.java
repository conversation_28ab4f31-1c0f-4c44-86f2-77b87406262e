package com.feidi.xx.cross.market.service;

import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCodeVo;

/**
 * 乘客推乘客邀请码Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IMktPassengerInviteCodeService {

    String createInviteCodeByUser(Long activityId, Long userId, String cityCode);

    /**
     * 根据邀请码查询邀请码信息
     *
     * @param inviteCode 邀请码
     * @return 邀请码信息
     */
    MktPassengerInviteCodeVo queryByCode(String inviteCode);
}
