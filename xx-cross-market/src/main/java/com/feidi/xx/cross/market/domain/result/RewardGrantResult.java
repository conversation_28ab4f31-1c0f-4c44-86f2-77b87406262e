package com.feidi.xx.cross.market.domain.result;

import lombok.Data;

/**
 * 奖励发放结果类
 * 
 * <AUTHOR>
 */
@Data
public class RewardGrantResult {
    
    /**
     * 发放是否成功
     */
    private boolean success;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 私有构造函数
     */
    private RewardGrantResult() {
    }
    
    /**
     * 私有构造函数
     * 
     * @param success 是否成功
     * @param errorMessage 错误消息
     * @param remark 备注信息
     */
    private RewardGrantResult(boolean success, String errorMessage, String remark) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.remark = remark;
    }
    
    /**
     * 创建成功的发放结果
     * 
     * @return 成功的发放结果
     */
    public static RewardGrantResult success() {
        return new RewardGrantResult(true, null, null);
    }
    
    /**
     * 创建成功的发放结果并携带备注
     * 
     * @param remark 备注信息
     * @return 成功的发放结果
     */
    public static RewardGrantResult success(String remark) {
        return new RewardGrantResult(true, null, remark);
    }
    
    /**
     * 创建失败的发放结果
     * 
     * @param errorMessage 错误消息
     * @return 失败的发放结果
     */
    public static RewardGrantResult fail(String errorMessage) {
        return new RewardGrantResult(false, errorMessage, null);
    }
    
    /**
     * 创建失败的发放结果并携带备注
     * 
     * @param errorMessage 错误消息
     * @param remark 备注信息
     * @return 失败的发放结果
     */
    public static RewardGrantResult fail(String errorMessage, String remark) {
        return new RewardGrantResult(false, errorMessage, remark);
    }
    
    /**
     * 判断发放是否失败
     * 
     * @return 是否失败
     */
    public boolean isFailed() {
        return !success;
    }
}
