package com.feidi.xx.cross.market.service.manager;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.result.RewardGrantResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardService;
import com.feidi.xx.cross.market.service.reward.CouponRewardStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategy;
import com.feidi.xx.cross.market.service.reward.RewardGrantStrategyContainer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 奖励发放管理器
 * 使用策略模式管理不同类型的奖励发放
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RewardGrantManager {

    private final IMktPassengerInviteRewardService rewardService;
    private final RewardGrantStrategyContainer strategyContainer;

    /**
     * 发放注册奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs  奖励配置列表
     * @param inviterId      邀请人ID
     * @param inviteeId      被邀请人ID
     * @return 发放是否成功
     */
    public boolean grantRegistrationRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs,
                                            Long inviterId, Long inviteeId) {
        log.info("开始发放注册奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);

        if (CollUtil.isEmpty(rewardConfigs)) {
            log.warn("奖励配置为空，无法发放注册奖励");
            return false;
        }

        boolean allSuccess = true;

        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigs) {
            // 只处理注册条件的奖励
            if (!PassengerInviteConditionTypeEnum.REGISTER.getCode().equals(rewardConfig.getConditionType())) {
                continue;
            }

            // 确定奖励接收人
            Long receiverId = determineReceiver(rewardConfig.getRoleType(), inviterId, inviteeId);
            if (receiverId == null) {
                log.warn("无法确定奖励接收人，跳过该奖励配置，角色类型：{}", rewardConfig.getRoleType());
                continue;
            }

            // 发放奖励
            boolean success = grantSingleReward(inviteRecordId, rewardConfig, receiverId);
            if (!success) {
                allSuccess = false;
                log.error("注册奖励发放失败，邀请记录ID：{}，奖励配置ID：{}，接收人ID：{}",
                        inviteRecordId, rewardConfig.getId(), receiverId);
            }
        }

        log.info("注册奖励发放完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
        return allSuccess;
    }

    /**
     * 发放首单奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfigs  奖励配置列表
     * @param inviterId      邀请人ID
     * @param inviteeId      被邀请人ID
     * @return 发放是否成功
     */
    public boolean grantFirstOrderRewards(Long inviteRecordId, List<MktPassengerInviteRewardConfigVo> rewardConfigs,
                                          Long inviterId, Long inviteeId) {
        log.info("开始发放首单奖励，邀请记录ID：{}，邀请人ID：{}，被邀请人ID：{}", inviteRecordId, inviterId, inviteeId);

        if (CollUtil.isEmpty(rewardConfigs)) {
            log.warn("奖励配置为空，无法发放首单奖励");
            return false;
        }

        boolean allSuccess = true;

        for (MktPassengerInviteRewardConfigVo rewardConfig : rewardConfigs) {
            // 只处理首单条件的奖励
            if (!PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(rewardConfig.getConditionType())) {
                continue;
            }

            // 确定奖励接收人
            Long receiverId = determineReceiver(rewardConfig.getRoleType(), inviterId, inviteeId);
            if (receiverId == null) {
                log.warn("无法确定奖励接收人，跳过该奖励配置，角色类型：{}", rewardConfig.getRoleType());
                continue;
            }

            // 发放奖励
            boolean success = grantSingleReward(inviteRecordId, rewardConfig, receiverId);
            if (!success) {
                allSuccess = false;
                log.error("首单奖励发放失败，邀请记录ID：{}，奖励配置ID：{}，接收人ID：{}",
                        inviteRecordId, rewardConfig.getId(), receiverId);
            }
        }

        log.info("首单奖励发放完成，邀请记录ID：{}，整体结果：{}", inviteRecordId, allSuccess ? "成功" : "部分失败");
        return allSuccess;
    }

    /**
     * 发放单个奖励
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfig   奖励配置
     * @param receiverId     接收人ID
     * @return 发放是否成功
     */
    private boolean grantSingleReward(Long inviteRecordId, MktPassengerInviteRewardConfigVo rewardConfig, Long receiverId) {
        try {
            log.info("开始发放单个奖励，邀请记录ID：{}，接收人ID：{}，奖励类型：{}",
                    inviteRecordId, receiverId, rewardConfig.getRewardType());

            // 1. 获取对应的策略
            RewardGrantStrategy strategy = strategyContainer.getStrategy(rewardConfig.getRewardType());
            if (strategy == null) {
                log.error("未找到对应的奖励发放策略，奖励类型：{}", rewardConfig.getRewardType());
                return false;
            }

            // 2. 先创建奖励记录，获取记录ID
            Long rewardRecordId = createRewardRecord(inviteRecordId, rewardConfig, receiverId);
            if (rewardRecordId == null) {
                log.error("创建奖励记录失败，邀请记录ID：{}，接收人ID：{}", inviteRecordId, receiverId);
                return false;
            }

            // 3. 执行奖励发放
            RewardGrantResult result = strategy.grantReward(receiverId, rewardConfig, rewardRecordId);

            // 4. 更新奖励记录状态
            boolean updateSuccess = updateRewardRecordStatus(rewardRecordId, result.isSuccess(), result.getRemark());

            if (result.isSuccess() && updateSuccess) {
                log.info("奖励发放成功，奖励记录ID：{}，接收人ID：{}，奖励类型：{}",
                        rewardRecordId, receiverId, rewardConfig.getRewardType());

                // 5. 如果是优惠券奖励，发送短信通知
                if (strategy instanceof CouponRewardStrategy couponRewardStrategy && CollUtil.isNotEmpty(rewardConfig.getRewardMeta().getCouponIds())) {
                    Long couponId = rewardConfig.getRewardMeta().getCouponIds().get(0);
                    couponRewardStrategy.sendSmsNotification(receiverId, couponId, rewardConfig.getConditionType());
                }

                return true;
            } else {
                log.error("奖励发放失败，奖励记录ID：{}，发放结果：{}，更新结果：{}",
                        rewardRecordId, result.isSuccess(), updateSuccess);
                return false;
            }

        } catch (Exception e) {
            log.error("发放奖励时发生异常，邀请记录ID：{}，接收人ID：{}", inviteRecordId, receiverId, e);
            return false;
        }
    }

    /**
     * 创建奖励记录
     *
     * @param inviteRecordId 邀请记录ID
     * @param rewardConfig   奖励配置
     * @param receiverId     接收人ID
     * @return 奖励记录ID
     */
    private Long createRewardRecord(Long inviteRecordId, MktPassengerInviteRewardConfigVo rewardConfig, Long receiverId) {
        try {
            log.debug("创建奖励记录，邀请记录ID：{}，接收人ID：{}，奖励类型：{}",
                    inviteRecordId, receiverId, rewardConfig.getRewardType());

            MktPassengerInviteRewardBo rewardBo = new MktPassengerInviteRewardBo();
            rewardBo.setInviteRecordId(inviteRecordId);
            rewardBo.setCampaignId(rewardConfig.getCampaignId());
            rewardBo.setPassengerId(receiverId);
            rewardBo.setRewardType(rewardConfig.getRewardType());
            rewardBo.setRewardValue(rewardConfig.getRewardValue());
            rewardBo.setRewardMeta(rewardConfig.getRewardMeta());
            rewardBo.setRoleType(rewardConfig.getRoleType());
            rewardBo.setConditionType(rewardConfig.getConditionType());
            rewardBo.setRewardConfigId(rewardConfig.getId());
            rewardBo.setStatus(PassengerInviteRewardStatusEnum.GRANTED.getCode()); // 初始状态为已发放

            Boolean created = rewardService.insertByBo(rewardBo);
            if (created) {
                log.debug("奖励记录创建成功，记录ID：{}", rewardBo.getId());
                return rewardBo.getId();
            } else {
                log.error("奖励记录创建失败");
                return null;
            }

        } catch (Exception e) {
            log.error("创建奖励记录异常，邀请记录ID：{}，接收人ID：{}", inviteRecordId, receiverId, e);
            return null;
        }
    }

    /**
     * 更新奖励记录状态
     *
     * @param rewardRecordId 奖励记录ID
     * @param grantSuccess   发放是否成功
     * @param remark         备注信息
     * @return 更新是否成功
     */
    private boolean updateRewardRecordStatus(Long rewardRecordId, boolean grantSuccess, String remark) {
        try {
            log.debug("更新奖励记录状态，记录ID：{}，发放结果：{}", rewardRecordId, grantSuccess);

            MktPassengerInviteRewardBo updateBo = new MktPassengerInviteRewardBo();
            updateBo.setId(rewardRecordId);
            updateBo.setStatus(grantSuccess ? PassengerInviteRewardStatusEnum.GRANTED.getCode()
                    : PassengerInviteRewardStatusEnum.GRANT_FAILED.getCode());
            updateBo.setRemark(remark);

            Boolean updated = rewardService.updateByBo(updateBo);
            if (updated) {
                log.debug("奖励记录状态更新成功，记录ID：{}", rewardRecordId);
                return true;
            } else {
                log.error("奖励记录状态更新失败，记录ID：{}", rewardRecordId);
                return false;
            }

        } catch (Exception e) {
            log.error("更新奖励记录状态异常，记录ID：{}", rewardRecordId, e);
            return false;
        }
    }

    /**
     * 根据角色类型确定奖励接收人
     *
     * @param roleType  角色类型
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 接收人ID
     */
    private Long determineReceiver(String roleType, Long inviterId, Long inviteeId) {
        if (PassengerInviteRoleTypeEnum.INVITER.getCode().equals(roleType)) {
            return inviterId;
        } else if (PassengerInviteRoleTypeEnum.INVITEE.getCode().equals(roleType)) {
            return inviteeId;
        } else {
            log.warn("未知的角色类型：{}", roleType);
            return null;
        }
    }
}
