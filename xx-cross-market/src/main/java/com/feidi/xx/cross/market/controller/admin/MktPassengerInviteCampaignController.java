package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.StopWatch;
import com.feidi.xx.common.core.constant.ModuleConstants;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.download.annotation.Download;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.excel.utils.ExcelUtil;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 后台 - 乘客推乘客活动
 * 前端访问路由地址为:/market/passengerInviteCampaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/passengerInviteCampaign")
public class MktPassengerInviteCampaignController extends BaseController {

    private final IMktPassengerInviteCampaignService mktPassengerInviteCampaignService;
    private final IMktPassengerInviteRecordService mktPassengerInviteRecordService;

    /**
     * 查询乘客推乘客活动列表
     */
    @SaCheckPermission("market:passengerInviteCampaign:list")
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MktPassengerInviteCampaignVo> list(MktPassengerInviteCampaignBo bo, PageQuery pageQuery) {
        return mktPassengerInviteCampaignService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取乘客推乘客活动详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:passengerInviteCampaign:query")
    @GetMapping("/{id}")
    @Enum2TextAspect
    public R<MktPassengerInviteCampaignVo> getInfo(@NotNull(message = "主键不能为空")
                                                   @PathVariable Long id) {
        return R.ok(mktPassengerInviteCampaignService.queryById(id));
    }

    /**
     * 新增乘客推乘客活动
     */
    @SaCheckPermission("market:passengerInviteCampaign:add")
    @Log(title = "乘客推乘客活动", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktPassengerInviteCampaignBo bo) {
        bo.valid();
        return toAjax(mktPassengerInviteCampaignService.insertByBo(bo));
    }

    /**
     * 修改乘客推乘客活动
     */
    @SaCheckPermission("market:passengerInviteCampaign:edit")
    @Log(title = "乘客推乘客活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktPassengerInviteCampaignBo bo) {
        bo.valid();
        return toAjax(mktPassengerInviteCampaignService.updateByBo(bo));
    }

    /**
     * 删除乘客推乘客活动
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:passengerInviteCampaign:remove")
    @Log(title = "乘客推乘客活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktPassengerInviteCampaignService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 更新乘客推乘客活动状态
     *
     * @param bo 包含id和status的业务对象
     * @return 操作结果
     */
    @SaCheckPermission("market:passengerInviteCampaign:updateStatus")
    @Log(title = "乘客推乘客活动状态更新", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody MktPassengerInviteCampaignBo bo) {
        Assert.notNull(bo.getId(), "活动ID不能为空");
        Assert.notNull(bo.getStatus(), "活动状态不能为空");

        // 验证状态值是否有效
        PassengerInviteCampaignStatusEnum.getByCodeThrow(bo.getStatus());

        mktPassengerInviteCampaignService.updateStatus(bo);
        return R.ok();
    }

    /**
     * 查询乘客推乘客数据记录列表
     *
     */
    @SaCheckPermission("market:passengerInviteCampaign:dataRecordList")
    @GetMapping("/dataRecordList")
    @Enum2TextAspect
    public TableDataInfo<MktPassengerInviteRecordDetailVo> dataRecordList(MktPassengerInviteRecordDetailQueryBo bo, PageQuery pageQuery) {
        return mktPassengerInviteRecordService.queryInviteRecordDetailPageList(bo, pageQuery);
    }

    /**
     * 导出乘客推乘客数据记录列表
     */
    @SaCheckPermission("market:passengerInviteCampaign:exportDataRecord")
    @PostMapping("/exportDataRecord")
    @Download(name = "乘客推乘客数据记录", module = ModuleConstants.MARKET, mode = "no", nameSpel = "'#{bo.tabStr()}'")
    public Object exportDataRecord(@RequestBody MktPassengerInviteRecordDetailQueryBo bo) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("查询数据");
        List<MktPassengerInviteRecordDetailVo> list = mktPassengerInviteRecordService.queryInviteRecordDetailList(bo);
        log.debug("数据记录总数 {}", list.size());
        stopWatch.stop();

        stopWatch.start("导出Excel");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        if (PassengerInviteConditionTypeEnum.REGISTER.getCode().equals(bo.getConditionType())) {
            ExcelUtil.exportExcel(list, bo.tabStr(), MktPassengerInviteRecordDetailVo.class, outputStream, null, List.of("orderCompleteTime"));
        } else if (PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode().equals(bo.getConditionType())) {
            ExcelUtil.exportExcel(list, bo.tabStr(), MktPassengerInviteRecordDetailVo.class, outputStream, null, List.of("inviteTime"));
        } else {
            ExcelUtil.exportExcel(list, bo.tabStr(), MktPassengerInviteRecordDetailVo.class, outputStream);
        }
        stopWatch.stop();

        log.info("导出询价记录耗时统计：\n{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        return outputStream.toByteArray();
    }
}
