package com.feidi.xx.cross.market.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.ShelvesStatusEnum;
import com.feidi.xx.cross.market.domain.MktActivity;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 活动业务对象 mkt_activity
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktActivity.class, reverseConvertGenerate = false)
public class MktActivityBo extends BaseEntity {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 代理id
     */
    private Long agentId;

    /**
     * 活动名称
     */
    @NotBlank(message = "活动名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 活动描述
     */
    private String refer;

    /**
     * 内容
     */
    private String content;

    /**
     * 广告图
     */
    private String adImage;

    /**
     * 活动方式[ActivityMannerEnum]
     */
    private String manner;

    /**
     * 范围
     * ActivityScopeEnum
     */
    private String scope;

    /**
     * 活动对象{用不上、已过时}
     */
    @Deprecated
    private String colony;

    /**
     * 活动类型{@link com.feidi.xx.cross.common.enums.market.ActivityTypeEnum}
     */
    @Deprecated
    private String type;

    /**
     * 优惠券发放范围{@link com.feidi.xx.cross.common.enums.market.GrantScopeEnum}
     */
    private String grantScope;
    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 投放渠道
     */
    @Deprecated
    private String issuingChannel;

    /**
     * 状态
     * {@link com.feidi.xx.cross.common.enums.market.ActivityStatusEnum}
     */
    private String status;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 当前时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nowTime;

    /**
     * ShelvesStatusEnum 上下架状态：0=下架，1=上架
     *
     * @see ShelvesStatusEnum
     */
    private String shelvesStatus;

    /**
     * 活动关联的优惠券ids
     */
    @NotEmpty(groups = {AddGroup.class}, message = "请添加优惠券！")
    @Max(groups = {AddGroup.class, EditGroup.class}, value = 10, message = "最多只能选择10张优惠券")
    private List<Long> couponIds;
}
