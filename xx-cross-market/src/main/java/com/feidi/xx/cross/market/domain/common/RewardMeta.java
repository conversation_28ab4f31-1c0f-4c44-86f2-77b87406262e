package com.feidi.xx.cross.market.domain.common;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RewardMeta {
    /**
     * 优惠券id列表
     */
    @NotEmpty(message = "优惠券id列表不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(max = 10, message = "优惠券id列表最多10个")
    private List<Long> couponIds = new ArrayList<>();

    public void setCouponIds(List<Long> couponIds) {
        //做去重
        this.couponIds = couponIds.stream().distinct().collect(Collectors.toList());
    }
}
