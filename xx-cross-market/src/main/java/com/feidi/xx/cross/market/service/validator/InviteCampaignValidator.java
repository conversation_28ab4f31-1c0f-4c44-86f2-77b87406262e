package com.feidi.xx.cross.market.service.validator;

import cn.hutool.core.collection.CollUtil;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.market.common.ValidationResult;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCodeVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCodeService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardConfigService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 邀请活动验证服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InviteCampaignValidator {

    private final IMktPassengerInviteCampaignService campaignService;
    private final IMktPassengerInviteCodeService inviteCodeService;
    private final IMktPassengerInviteRewardConfigService inviteRewardConfigService;
    private final IMktPassengerInviteRecordService inviteRecordService;

    /**
     * 验证数据类，用于封装验证过程中的相关数据
     */
    @Setter
    @Getter
    public static class RegistrationValidationData {
        // getters and setters
        private MktPassengerInviteCampaignVo campaign;
        private List<MktPassengerInviteRewardConfigVo> rewardConfigs;
        private MktPassengerInviteCodeVo inviteCode;

    }

    /**
     * 首单验证数据类
     */
    @Setter
    @Getter
    public static class FirstOrderValidationData {
        // getters and setters
        private MktPassengerInviteRecordVo inviteRecord;
        private MktPassengerInviteCampaignVo campaign;
        private List<MktPassengerInviteRewardConfigVo> rewardConfigs;

    }

    /**
     * 验证乘客注册参与活动的前置条件
     *
     * @param passengerId   乘客ID
     * @param inviteCodeStr 邀请码
     * @return 验证结果
     */
    public ValidationResult<RegistrationValidationData> validateRegistrationParticipation(Long passengerId, String inviteCodeStr) {
        log.debug("开始验证乘客注册参与活动，乘客ID：{}，邀请码：{}", passengerId, inviteCodeStr);

        try {
            // 1. 验证邀请码是否为空
            if (inviteCodeStr == null) {
                log.debug("邀请码为空，乘客ID：{}", passengerId);
                return ValidationResult.fail("邀请码不能为空");
            }

            // 2. 验证乘客是否已参与过活动
            boolean isJoin = inviteRecordService.isJoin(passengerId);
            if (isJoin) {
                log.warn("乘客已参与过活动，乘客ID：{}", passengerId);
                return ValidationResult.fail("乘客已参与过活动");
            }

            // 3. 查询活动信息
            MktPassengerInviteCampaignVo campaign = campaignService.queryByInviteCode(inviteCodeStr);
            if (campaign == null) {
                log.warn("邀请码对应的活动不存在，邀请码：{}", inviteCodeStr);
                return ValidationResult.fail("邀请码对应的活动不存在");
            }

            // 4. 检查活动状态
            if (!Objects.equals(campaign.getStatus(), PassengerInviteCampaignStatusEnum.ONGOING.getCode())) {
                log.warn("活动不在进行中，活动ID：{}，状态：{}", campaign.getId(), campaign.getStatus());
                return ValidationResult.fail("活动不在进行中");
            }

            // 5. 查询奖励配置
            List<MktPassengerInviteRewardConfigVo> rewardConfigs = inviteRewardConfigService.queryListByCampaignId(campaign.getId());
            if (CollUtil.isEmpty(rewardConfigs)) {
                log.warn("活动奖励配置不存在，活动ID：{}", campaign.getId());
                return ValidationResult.fail("活动奖励配置不存在");
            }
            //校验未配置注册奖励
            if (rewardConfigs.stream().noneMatch(config -> config.getConditionType().equals(PassengerInviteConditionTypeEnum.REGISTER.getCode()))) {
                log.warn("活动未配置注册奖励，活动ID：{}", campaign.getId());
                return ValidationResult.fail("活动未配置注册奖励");
            }

            // 6. 查询邀请码信息
            MktPassengerInviteCodeVo inviteCode = inviteCodeService.queryByCode(inviteCodeStr);
            if (inviteCode == null) {
                log.warn("邀请码不存在，邀请码：{}", inviteCodeStr);
                return ValidationResult.fail("邀请码不存在");
            }

            // 7. 封装验证数据
            RegistrationValidationData data = new RegistrationValidationData();
            data.setCampaign(campaign);
            data.setRewardConfigs(rewardConfigs);
            data.setInviteCode(inviteCode);

            log.debug("乘客注册参与活动验证通过，乘客ID：{}，活动ID：{}", passengerId, campaign.getId());
            return ValidationResult.success(data);

        } catch (Exception e) {
            log.error("验证乘客注册参与活动时发生异常，乘客ID：{}，邀请码：{}", passengerId, inviteCodeStr, e);
            return ValidationResult.fail("验证过程中发生异常：" + e.getMessage());
        }
    }

    /**
     * 验证首单完成的前置条件
     *
     * @param passengerId 乘客ID
     * @param orderNo     订单号
     * @return 验证结果
     */
    public ValidationResult<FirstOrderValidationData> validateFirstOrderCompletion(Long passengerId, String orderNo) {
        log.debug("开始验证首单完成前置条件，乘客ID：{}，订单号：{}", passengerId, orderNo);

        try {
            // 1. 查询邀请记录
            MktPassengerInviteRecordVo inviteRecord = inviteRecordService.queryByInviteeId(passengerId);
            if (inviteRecord == null) {
                log.debug("乘客没有邀请记录，乘客ID：{}", passengerId);
                return ValidationResult.fail("乘客没有邀请记录");
            }

            // 2. 查询活动信息
            MktPassengerInviteCampaignVo campaign = campaignService.queryById(inviteRecord.getCampaignId());
            if (campaign == null) {
                log.warn("邀请记录对应的活动不存在，邀请记录ID：{}，活动ID：{}", inviteRecord.getId(), inviteRecord.getCampaignId());
                return ValidationResult.fail("邀请记录对应的活动不存在");
            }

            // 3. 检查活动状态
            if (!Objects.equals(campaign.getStatus(), PassengerInviteCampaignStatusEnum.ONGOING.getCode())) {
                log.warn("活动不在进行中，活动ID：{}，状态：{}", campaign.getId(), campaign.getStatus());
                return ValidationResult.fail("活动不在进行中");
            }

            // 4. 查询奖励配置
            List<MktPassengerInviteRewardConfigVo> rewardConfigs = inviteRewardConfigService.queryListByCampaignId(inviteRecord.getCampaignId());
            if (CollUtil.isEmpty(rewardConfigs)) {
                log.warn("活动奖励配置不存在，活动ID：{}", inviteRecord.getCampaignId());
                return ValidationResult.fail("活动奖励配置不存在");
            }
            //校验未配置首单奖励
            if (rewardConfigs.stream().noneMatch(config -> config.getConditionType().equals(PassengerInviteConditionTypeEnum.FIRST_ORDER.getCode()))) {
                log.warn("活动未配置首单奖励，活动ID：{}", inviteRecord.getCampaignId());
                return ValidationResult.fail("活动未配置首单奖励");
            }

            // 5. 封装验证数据
            FirstOrderValidationData data = new FirstOrderValidationData();
            data.setInviteRecord(inviteRecord);
            data.setCampaign(campaign);
            data.setRewardConfigs(rewardConfigs);

            log.debug("首单完成前置条件验证通过，乘客ID：{}，邀请记录ID：{}", passengerId, inviteRecord.getId());
            return ValidationResult.success(data);

        } catch (Exception e) {
            log.error("验证首单完成前置条件时发生异常，乘客ID：{}，订单号：{}", passengerId, orderNo, e);
            return ValidationResult.fail("验证过程中发生异常：" + e.getMessage());
        }
    }
}
