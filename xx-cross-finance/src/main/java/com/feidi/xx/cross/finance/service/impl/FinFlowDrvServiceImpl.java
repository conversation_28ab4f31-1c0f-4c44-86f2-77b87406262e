package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.collection.CollUtil;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.core.enums.JoinEnum;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.finance.CashAuditStatusEnum;
import com.feidi.xx.cross.common.enums.finance.CashTransStatusEnum;
import com.feidi.xx.cross.finance.domain.FinCash;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import com.feidi.xx.cross.finance.domain.bo.FinFlowDrvBo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinCashFlowDrvVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinFlowIncomeOrderVo;
import com.feidi.xx.cross.finance.domain.vo.driver.FinTransferFlowDrvVo;
import com.feidi.xx.cross.finance.mapper.FinCashMapper;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import com.feidi.xx.cross.finance.mapper.FinTransferMapper;
import com.feidi.xx.cross.finance.service.IFinFlowDrvService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FinFlowDrvServiceImpl implements IFinFlowDrvService {

    private final FinFlowMapper baseMapper;

    private final FinCashMapper cashMapper;

    private final FinTransferMapper transferMapper;

    @DubboReference
    private final RemoteOrderService remoteOrderService;

    @DubboReference
    private final RemoteAgentService remoteAgentService;

    @Override
    public TableDataInfo<FinFlowIncomeOrderVo> income(FinFlowDrvBo bo, PageQuery pageQuery) {
        Long driverId = LoginHelper.getUserId();
        IPage<FinFlow> iPage = baseMapper.listByJoinExceptCash(pageQuery.build(), driverId, JoinEnum.ORDER);
        List<FinFlow> records = iPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> joinIds = records.stream().map(FinFlow::getJoinId).collect(Collectors.toSet());
            List<RemoteOrderVo> orderVos = remoteOrderService.listByIds(joinIds);
            List<Long> agentIds = StreamUtils.toList(orderVos, RemoteOrderVo::getAgentId);
            Map<Long, String> agentMap = new HashMap<>();
            if (CollUtil.isNotEmpty(agentIds)) {
                List<RemoteAgentVo> agents = remoteAgentService.getAgentInfoById(agentIds);
                agentMap = CollUtil.toMap(agents, new HashMap<>(), RemoteAgentVo::getId, RemoteAgentVo::getCompanyName);
            }

            Map<Long, RemoteOrderVo> orderMap = orderVos.stream().collect(Collectors.toMap(RemoteOrderVo::getId, v -> v));
            List<FinFlowIncomeOrderVo> ret = new ArrayList<>();
            for (FinFlow flow : records) {
                FinFlowIncomeOrderVo vo = FinFlowIncomeOrderVo.create(flow, FinFlowIncomeOrderVo.class);
                // 关联表信息
                RemoteOrderVo orderVo = orderMap.get(flow.getJoinId());
                vo.setId(orderVo.getId());
                vo.setNo(orderVo.getOrderNo());
                vo.setAgentName(agentMap.get(orderVo.getAgentId()));
                // TODO-NEW 后期再修改
                vo.setStart(orderVo.getStart());
                vo.setEnd(orderVo.getEnd());
                ret.add(vo);
            }
            return new TableDataInfo<>(ret, iPage.getTotal());
        }
        return new TableDataInfo<>();
    }

    @Override
    public TableDataInfo<FinCashFlowDrvVo> cash(FinFlowDrvBo bo, PageQuery pageQuery) {
        Long driverId = LoginHelper.getUserId();
        IPage<FinCash> iPage = cashMapper.pageByDriverId(driverId, pageQuery.build());
        List<FinCash> records = iPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<Long> agentIds = StreamUtils.toList(records, FinCash::getAgentId);
            Map<Long, String> agentMap = new HashMap<>();
            if (CollUtil.isNotEmpty(agentIds)) {
                List<RemoteAgentVo> agents = remoteAgentService.getAgentInfoById(agentIds);
                agentMap = CollUtil.toMap(agents, new HashMap<>(), RemoteAgentVo::getId, RemoteAgentVo::getCompanyName);
            }

            List<FinCashFlowDrvVo> ret = new ArrayList<>();
            for (FinCash cash : records) {
                FinCashFlowDrvVo vo = new FinCashFlowDrvVo();
                vo.setId(cash.getId());
                vo.setNo(cash.getCashNo());
                vo.setAgentName(agentMap.get(cash.getAgentId()));
                vo.setName(cash.getName());
                vo.setAmount(cash.getAmount());
                vo.setCreateTime(cash.getCreateTime());
                vo.setAccountType(cash.getAccountType());
                vo.setAccount(DesensitizedUtil.mobilePhone(cash.getAccount()));
                vo.setIsFreeze(CashAuditStatusEnum.ING.getCode().equals(cash.getStatus()));
                // 状态
                CashAuditStatusEnum auditStatus = CashAuditStatusEnum.getByCode(cash.getStatus());
                CashTransStatusEnum transStatus = CashTransStatusEnum.getByCode(cash.getTransStatus());
                if (CashAuditStatusEnum.SUCCESS.equals(auditStatus) && CashTransStatusEnum.TRADE_SUCCESS.equals(transStatus)) {
                    vo.setStatus("提现成功");
                } else if (CashAuditStatusEnum.REJECT.equals(auditStatus)) {
                    vo.setStatus("提现失败");
                    vo.setRemark(cash.getRemark());
                } else {
                    vo.setStatus("审核中");
                }
                ret.add(vo);
            }
            return new TableDataInfo<>(ret, iPage.getTotal());
        }
        return new TableDataInfo<>();
    }

    @Override
    public TableDataInfo<FinTransferFlowDrvVo> transfer(FinFlowDrvBo bo, PageQuery pageQuery) {
        Long driverId = LoginHelper.getUserId();
        IPage<FinFlow> iPage = baseMapper.listByJoinExceptCash(pageQuery.build(), driverId, JoinEnum.TRANSFER);
        List<FinFlow> records = iPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<Long> joinIds = records.stream().map(FinFlow::getJoinId).collect(Collectors.toSet());
            List<FinTransfer> transfers = transferMapper.selectBatchIds(joinIds);
            Map<Long, FinTransfer> transferMap = transfers.stream().collect(Collectors.toMap(FinTransfer::getId, v -> v));

            List<Long> agentIds = StreamUtils.toList(transfers, FinTransfer::getAgentId);
            Map<Long, String> agentMap = new HashMap<>();
            if (CollUtil.isNotEmpty(agentIds)) {
                List<RemoteAgentVo> agents = remoteAgentService.getAgentInfoById(agentIds);
                agentMap = CollUtil.toMap(agents, new HashMap<>(), RemoteAgentVo::getId, RemoteAgentVo::getCompanyName);
            }

            List<FinTransferFlowDrvVo> ret = new ArrayList<>();
            for (FinFlow flow : records) {
                FinTransferFlowDrvVo vo = FinTransferFlowDrvVo.create(flow, FinTransferFlowDrvVo.class);
                // 关联表信息
                FinTransfer transfer = transferMap.get(flow.getJoinId());
                vo.setId(transfer.getId());
                vo.setNo(transfer.getTransferNo());
                vo.setAgentName(agentMap.get(transfer.getAgentId()));
                vo.setAmount(flow.getAmount());
                if (DirectionEnum.IN.getCode().equals(flow.getDirection())) {
                    // 来自谁
                    vo.setDriverName(transfer.getOutDriverName());
                    vo.setDriverPhone(DesensitizedUtil.mobilePhone(transfer.getOutDriverPhone()));
                } else if (DirectionEnum.OUT.getCode().equals(flow.getDirection())) {
                    // 转给谁
                    vo.setDriverName(transfer.getInDriverName());
                    vo.setDriverPhone(DesensitizedUtil.mobilePhone(transfer.getInDriverPhone()));
                }
                ret.add(vo);
            }
            return new TableDataInfo<>(ret, iPage.getTotal());
        }
        return new TableDataInfo<>();
    }


}
