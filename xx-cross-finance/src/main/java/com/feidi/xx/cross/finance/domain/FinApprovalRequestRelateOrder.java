package com.feidi.xx.cross.finance.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 调账关联信息
 *
 * <AUTHOR>
 */
@Data
public class FinApprovalRequestRelateOrder {

    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orderNo;
    /**
     * 调整金额
     */
    @NotNull(message = "调整金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long adjustAmount;

    /**
     * 交易说明
     */
    private String transactionNote;

    /**
     * 异常原因
     */
    private String exceptionReason;

    /**
     * 时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date adjustmentDate;
}
