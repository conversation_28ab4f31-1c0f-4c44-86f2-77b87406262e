package com.feidi.xx.cross.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.enums.DirectionEnum;
import com.feidi.xx.common.core.enums.JoinEnum;
import com.feidi.xx.common.core.enums.UserTypeEnum;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.CollUtils;
import com.feidi.xx.common.core.utils.DateUtils;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.ObjectUtils;
import com.feidi.xx.common.core.utils.xx.MoneyConvertUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.cache.order.manager.OrdCacheManager;
import com.feidi.xx.cross.common.enums.finance.FlowSelectEnum;
import com.feidi.xx.cross.common.enums.finance.FlowShowStatusEnum;
import com.feidi.xx.cross.common.enums.finance.FlowStatusEnum;
import com.feidi.xx.cross.common.enums.finance.FlowTypeEnum;
import com.feidi.xx.cross.common.enums.order.RateTypeEnum;
import com.feidi.xx.cross.common.utils.order.OrderUtils;
import com.feidi.xx.cross.finance.domain.FinCash;
import com.feidi.xx.cross.finance.domain.FinFlow;
import com.feidi.xx.cross.finance.domain.FinTransfer;
import com.feidi.xx.cross.finance.domain.bo.FinApprovalRequestsBo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowBo;
import com.feidi.xx.cross.finance.domain.bo.FinFlowQueryBo;
import com.feidi.xx.cross.finance.domain.factory.FinFlowFactory;
import com.feidi.xx.cross.finance.domain.vo.FinFlowExportVo;
import com.feidi.xx.cross.finance.domain.vo.FinFlowVo;
import com.feidi.xx.cross.finance.mapper.FinCashMapper;
import com.feidi.xx.cross.finance.mapper.FinDrvWalletMapper;
import com.feidi.xx.cross.finance.mapper.FinFlowMapper;
import com.feidi.xx.cross.finance.mapper.FinTransferMapper;
import com.feidi.xx.cross.finance.service.IFinFlowService;
import com.feidi.xx.cross.order.api.RemoteOrderService;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderDetailVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderRateVo;
import com.feidi.xx.cross.order.api.domain.vo.RemoteOrderVo;
import com.feidi.xx.cross.power.api.RemoteAgentService;
import com.feidi.xx.cross.power.api.domain.agent.bo.RemoteAgentVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资金流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinFlowServiceImpl implements IFinFlowService {

    private final OrdCacheManager ordCacheManager;
    private final FinFlowMapper baseMapper;
    private final FinCashMapper cashMapper;
    private final FinTransferMapper transferMapper;
    @DubboReference
    private final RemoteAgentService remoteAgentService;
    @DubboReference
    private final RemoteOrderService remoteOrderService;
    private final FinDrvWalletMapper drvWalletMapper;

    /**
     * 查询资金流水
     *
     * @param id 主键
     * @return 资金流水
     */
    @Override
    public FinFlowVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询资金流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 资金流水分页列表
     */
    @Override
    public TableDataInfo<FinFlowVo> queryPageList(FinFlowQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinFlow> lqw = buildQueryWrapper(bo);
        Page<FinFlowVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<FinFlowVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            // 代理商
            List<RemoteAgentVo> agents = remoteAgentService.getAllAgentInfo();
            Map<Long, String> agentMap = CollUtil.toMap(agents, new HashMap<>(), RemoteAgentVo::getId, RemoteAgentVo::getCompanyName);
            for (FinFlowVo record : records) {
                record.setAgentName(agentMap.get(record.getAgentId()));
                // TODO: 2024/11/5 订单单号后期改为id
                if (JoinEnum.ORDER.getCode().equals(record.getJoinTable())) {
                    record.setJoinId(record.getJoinId());
                }
                if (ObjectUtils.isNotNull(bo) && ObjectUtils.isNotNull(bo.getType()) && FlowSelectEnum.INVITE.equals(bo.getType())) {
                    if (ObjectUtils.isNotNull(record.getJoinId())) {
                        result.getRecords().removeIf(item ->
                                item.getAmount() == null || BigDecimal.ZERO.compareTo(BigDecimal.valueOf(item.getAmount())) == 0
                        );
                        RemoteOrderDetailVo remoteOrderDetailVo = remoteOrderService.queryById(Long.valueOf(record.getJoinId()));
                        if (ObjectUtil.isNotNull(remoteOrderDetailVo)) {
                            if (ObjectUtils.isNotNull(remoteOrderDetailVo.getPassengerPhone())) {
                                record.setPassengerName(OrderUtils.getPassengerName(remoteOrderDetailVo.getPassengerPhone()));
                            }
                        }
                    }
                }
            }
        }
        return TableDataInfo.build(records, result.getTotal());
    }

    /**
     * 查询符合条件的资金流水列表
     *
     * @param bo 查询条件
     * @return 资金流水列表
     */
    @Override
    public List<FinFlowExportVo> queryList(FinFlowQueryBo bo) {
        //Assert.isTrue(bo.getDriverId() != null, "请筛选导出的司机");
        //Assert.isTrue(bo.getStartTime() != null && bo.getEndTime() != null, "时间范围不能为空");
        //Assert.isTrue(DateUtil.betweenDay(bo.getStartTime(), bo.getEndTime(), true) <= 31, "时间范围不能超过31天");

        LambdaQueryWrapper<FinFlow> lqw = buildQueryWrapper(bo);
        List<FinFlow> flowVos = baseMapper.selectList(lqw);
        List<FinFlowExportVo> convert = MapstructUtils.convert(flowVos, FinFlowExportVo.class);
        if (CollUtil.isNotEmpty(convert)) {
            // 代理商
            List<RemoteAgentVo> agents = remoteAgentService.getAllAgentInfo();
            Map<Long, String> agentMap = CollUtil.toMap(agents, new HashMap<>(), RemoteAgentVo::getId, RemoteAgentVo::getCompanyName);
            // 提现
            List<String> cashIds = convert.stream().filter(e -> JoinEnum.CASH.getCode().equals(e.getJoinTable())).map(FinFlowExportVo::getJoinId).distinct().toList();
            List<FinCash> cashes = new ArrayList<>();
            if (CollUtil.isNotEmpty(cashIds)) {
                cashes = cashMapper.selectBatchIds(cashIds);
            }
            Map<Long, FinCash> cashMap = CollUtil.toMap(cashes, new HashMap<>(), FinCash::getId);
            // 转账
            List<String> transferIds = convert.stream().filter(e -> JoinEnum.TRANSFER.getCode().equals(e.getJoinTable())).map(FinFlowExportVo::getJoinId).distinct().toList();
            List<FinTransfer> transfers = new ArrayList<>();
            if (CollUtil.isNotEmpty(transferIds)) {
                transfers = transferMapper.selectBatchIds(transferIds);
            }
            Map<Long, FinTransfer> transferMap = CollUtil.toMap(transfers, new HashMap<>(), FinTransfer::getId);
            // 订单
            List<Long> orderIds = convert.parallelStream().filter(e -> JoinEnum.ORDER.getCode().equals(e.getJoinTable())).map(FinFlowExportVo::getJoinId).map(Convert::toLong).distinct().toList();
            List<RemoteOrderVo> orders = new ArrayList<>();
            if (CollUtil.isNotEmpty(orderIds)) {
                orders = remoteOrderService.listByIds(orderIds);
            }
            Map<Long, RemoteOrderVo> orderMap = CollUtil.toMap(orders, new HashMap<>(), RemoteOrderVo::getId);
            // 填充
            convert.parallelStream().forEach(exportVo -> {
                exportVo.setAgentName(agentMap.get(exportVo.getAgentId()));
                if (JoinEnum.CASH.getCode().equals(exportVo.getJoinTable())) {
                    FinCash cash = cashMap.get(Long.valueOf(exportVo.getJoinId()));
                    if (cash != null) {
                        exportVo.setTradeNo(cash.getFlowNo());
                    }
                } else if (JoinEnum.TRANSFER.getCode().equals(exportVo.getJoinTable())) {
                    FinTransfer transfer = transferMap.get(Long.valueOf(exportVo.getJoinId()));
                    if (transfer != null) {
                        if (DirectionEnum.IN.getCode().equals(exportVo.getDirection())) {
                            exportVo.setTargetDriverId(transfer.getOutDriverId());
                            exportVo.setTargetDriverName(transfer.getOutDriverName());
                            exportVo.setTargetDriverPhone(transfer.getOutDriverPhone());
                        } else {
                            exportVo.setTargetDriverId(transfer.getInDriverId());
                            exportVo.setTargetDriverName(transfer.getInDriverName());
                            exportVo.setTargetDriverPhone(transfer.getInDriverPhone());
                        }
                    }
                } else if (JoinEnum.ORDER.getCode().equals(exportVo.getJoinTable())) {
                    RemoteOrderVo order = orderMap.get(Long.valueOf(exportVo.getJoinId()));
                    if (order != null) {
                        exportVo.setOrderPrice(MoneyConvertUtils.fen2YuanBigDecimal(order.getOrderPrice()));

                        Map<String, RemoteOrderRateVo> rateType2Map = ordCacheManager.getOrderRateInfoByOrderId(order.getId())
                                .stream().collect(Collectors.toMap(RemoteOrderRateVo::getRateType, Function.identity(), (v1, v2) -> v2));
                        RemoteOrderRateVo agentOrderRateVo = rateType2Map.get(RateTypeEnum.AGENT.getCode());
                        if (ObjectUtils.isNotNull(agentOrderRateVo)) {
                            exportVo.setAgentRate(agentOrderRateVo.getRate());
                            exportVo.setAgentProfit(MoneyConvertUtils.fen2YuanBigDecimal(agentOrderRateVo.getAmount()));
                        }
                        RemoteOrderRateVo driverOrderRateVo = rateType2Map.get(RateTypeEnum.DRIVER.getCode());
                        if (ObjectUtils.isNotNull(driverOrderRateVo)) {
                            exportVo.setDriverRate(driverOrderRateVo.getRate());
                            exportVo.setDriverProfit(MoneyConvertUtils.fen2YuanBigDecimal(driverOrderRateVo.getAmount()));
                        }
                    }
                    // TODO: 2024/11/5 订单单号后期改为id
                    exportVo.setJoinId(exportVo.getJoinNo());
                }
            });
        }
        return convert;
    }

    private LambdaQueryWrapper<FinFlow> buildQueryWrapper(FinFlowQueryBo bo) {
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.nested(ObjectUtil.isNotNull(bo.getUnionId()),
                l -> l.eq(FinFlow::getId, bo.getUnionId()).or()
                        .eq(FinFlow::getJoinId, bo.getUnionId()).or()
                        .eq(FinFlow::getFlowNo, bo.getUnionId()).or()
                        .eq(FinFlow::getJoinNo, bo.getUnionId()).or()
                        .eq(FinFlow::getDriverId, bo.getUnionId()).or()
                        .like(FinFlow::getDriverName, bo.getUnionId()).or()
                        .like(FinFlow::getDriverPhone, bo.getUnionId())
        );

        lqw.eq(ObjectUtil.isNotNull(bo.getDriverId()), FinFlow::getDriverId, bo.getDriverId());
        lqw.eq(StrUtil.isNotBlank(bo.getDirection()), FinFlow::getDirection, bo.getDirection());
        lqw.ge(ObjectUtil.isNotNull(bo.getStartTime()), FinFlow::getCreateTime, bo.getStartTime());
        lqw.le(ObjectUtil.isNotNull(bo.getEndTime()), FinFlow::getCreateTime, bo.getEndTime());

        if (CollUtil.isNotEmpty(bo.getAgentIds())) {
            lqw.in(bo.getAgentIds() != null, FinFlow::getAgentId, bo.getAgentIds());
        } else {
            if (UserTypeEnum.AGENT_USER.equals(LoginHelper.getUserType())) {
                lqw.nested(bo.getAgentId() != null, l -> {
                    l.eq(FinFlow::getAgentId, bo.getAgentId());
                });
            } else {
                lqw.eq(bo.getAgentId() != null, FinFlow::getAgentId, bo.getAgentId());
            }
        }

        if (bo.getType() != null) {
            lqw.in(FinFlow::getType, bo.getType().getType().stream().map(FlowTypeEnum::getCode).toList());
        }
        if (bo.getStatus() != null) {
            if (bo.getType() == null) {
                throw new ServiceException("请选择类型");
            } else {
                FlowStatusEnum status = FlowStatusEnum.getCorrectStatus(bo.getType(), bo.getStatus());
                if (FlowStatusEnum.isTypeForFinished(bo.getType())) {
                    // 不为已完成才查询
                    if (!bo.getStatus().equals(FlowShowStatusEnum.FINISHED)) {
                        lqw.eq(FinFlow::getType, status.getType() != null ? status.getType().getCode() : null);
                    }
                } else {
                    lqw.eq(FinFlow::getType, status.getType() != null ? status.getType().getCode() : null);
                }
            }
        }

        // 过滤仅变动可提现金额的
        lqw.ne(FinFlow::getAmount, 0);
        // 排序
        lqw.orderByDesc(FinFlow::getCreateTime);
        lqw.orderByDesc(FinFlow::getId);
        return lqw;
    }

    /**
     * 新增资金流水
     *
     * @param bo 资金流水
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinFlowBo bo) {
        FinFlow add = MapstructUtils.convert(bo, FinFlow.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        return flag;
    }

    /**
     * 修改资金流水
     *
     * @param bo 资金流水
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinFlowBo bo) {
        FinFlow update = MapstructUtils.convert(bo, FinFlow.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinFlow entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除资金流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据流水类型查询今日流水
     *
     * @param flowTypes 流水类型
     * @return 今日流水集合
     */
    @Override
    public List<FinFlow> queryTodayFlowByTypes(List<String> flowTypes) {
        if (CollUtils.isEmpty(flowTypes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.in(FinFlow::getType, flowTypes)
                .ge(FinFlow::getCreateTime, DateUtils.date2String(DateUtils.getDayBegin()))
                .le(FinFlow::getCreateTime, DateUtils.date2String(DateUtils.getDayEnd()));

        return baseMapper.selectList(lqw);
    }

    /**
     * 根据订单ID和流水类型查询流水
     *
     * @param orderIds 订单id集合
     * @param flowType 流水类型
     * @return 流水集合
     */
    @Override
    public List<FinFlow> queryByOrderIdsAndType(List<Long> orderIds, String flowType) {
        if (CollUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.in(FinFlow::getJoinId, orderIds)
                .eq(FinFlow::getType, flowType)
                .eq(FinFlow::getDirection, DirectionEnum.IN.getCode());
        return baseMapper.selectList(lqw);
    }

    /**
     * 人工调账新增流水
     *  TODO R 考虑修改为消息队列异步处理、针对全局流水创建按司机id加锁处理
     *
     * @param finApprovalRequests 审批记录
     */
    @Override
    public void artificialAdjustmentAddFlow(FinApprovalRequestsBo finApprovalRequests) {
        log.info("人工调账新增流水开始 - 申请id: {}", finApprovalRequests.getId());
        for (var detail : finApprovalRequests.getDetails()) {
            for (var relateOrder : detail.getRelateOrders()) {
                FinFlow lastFlow = baseMapper.getLastFlow(detail.getDriverId());
                if (log.isDebugEnabled()) {
                    log.debug("aid {} detid {} 上一条流水：{}", finApprovalRequests.getId(), detail.getId(), JSONUtil.toJsonStr(lastFlow));
                }
                FinFlow nextFlow = FinFlowFactory.createFlow(lastFlow, FinFlowFactory.FlowCreateType.ARTIFICIAL_ADJUSTMENT,
                        relateOrder.getAdjustAmount() > 0 ? DirectionEnum.IN : DirectionEnum.OUT,
                        FlowTypeEnum.ARTIFICIAL_ADJUSTMENT,
                        finApprovalRequests, detail, relateOrder);
                if (log.isDebugEnabled()) {
                    log.debug("人工调账流水：{}", JSONUtil.toJsonStr(nextFlow));
                }
                boolean flag = baseMapper.insert(nextFlow) > 0;
                if (flag) {
                    drvWalletMapper.updateByFlow(nextFlow);
                }
            }
        }
        log.info("人工调账新增流水成功 - 申请id: {}", finApprovalRequests.getId() );
    }

    @Override
    public List<FinFlow> queryByOrderIdsAndTypes(List<Long> orderIds, List<String> flowTypes) {
        if (CollUtils.isEmpty(orderIds) || CollUtils.isEmpty(flowTypes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<FinFlow> lqw = Wrappers.lambdaQuery();
        lqw.in(FinFlow::getJoinId, orderIds)
                .in(FinFlow::getType, flowTypes)
                .eq(FinFlow::getDirection, DirectionEnum.IN.getCode());

        return baseMapper.selectList(lqw);
    }
}
