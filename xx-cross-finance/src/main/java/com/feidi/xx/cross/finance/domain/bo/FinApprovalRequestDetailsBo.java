package com.feidi.xx.cross.finance.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestDetails;
import com.feidi.xx.cross.finance.domain.FinApprovalRequestRelateOrder;
import com.feidi.xx.cross.finance.domain.FinDrvWallet;
import com.feidi.xx.cross.power.api.domain.driver.vo.RemoteDriverVo;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 账单审批关联订单业务对象 fin_approval_request_details
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinApprovalRequestDetails.class, reverseConvertGenerate = false)
public class FinApprovalRequestDetailsBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 账单审批记录ID
     */
    private Long approvalRequestId;
    /**
     * 司机id
     */
    @NotNull(message = "司机id不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long driverId;
    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 司机姓名
     */
    private String driverName;
    /**
     * 关联订单
     */
    @NotEmpty(message = "关联订单不能为空", groups = {AddGroup.class, EditGroup.class})
    @Valid
    private List<FinApprovalRequestRelateOrder> relateOrders;

    /**
     * 交易类型，1:人工调账
     */
    @NotNull(message = "交易类型，1:人工调账不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer transactionType;

    /**
     * 账户余额(冻结金额+可提现金额)
     */
    @NotNull(message = "账户余额(冻结金额+可提现金额)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long accountAmount;

    /**
     * 可提现金额
     */
    @NotNull(message = "可提现金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long balance;

    /**
     * 冻结金额
     */
    @NotNull(message = "冻结金额不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long freeze;
    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 代理商id
     */
    private Long agentId;

    public Long getSumAmount() {
        return relateOrders.stream().mapToLong(FinApprovalRequestRelateOrder::getAdjustAmount).sum();
    }

    public void setAssociatedInfo(RemoteDriverVo driverVo, FinDrvWallet finDrvWallet) {
        this.setDriverName(driverVo.getName());
        this.setAgentId(driverVo.getAgentId());
        this.setDriverPhone(driverVo.getPhone());
        this.setBalance(finDrvWallet.getBalance());
        this.setFreeze(finDrvWallet.getFreeze());
        this.setAccountAmount(finDrvWallet.getAccountAmount());
    }
}
