package com.feidi.xx.cross.member.mq;

import com.feidi.xx.common.core.utils.ValidatorUtils;
import com.feidi.xx.common.rocketmq.base.BaseSendExtendDTO;
import com.feidi.xx.common.rocketmq.enums.MqMessageTypeEnum;
import com.feidi.xx.common.rocketmq.producer.AbstractProducer;
import com.feidi.xx.common.rocketmq.util.MQMessageUtil;
import com.feidi.xx.cross.common.constant.member.MemberRocketMQConstant;
import com.feidi.xx.cross.common.mq.event.PassengerRegisterEvent;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

@Component
public class PassengerRegisterProducer extends AbstractProducer<PassengerRegisterEvent> {
    private BaseSendExtendDTO buildBaseSendExtendParam(PassengerRegisterEvent inviteEvent) {
        ValidatorUtils.validate(inviteEvent);
        return BaseSendExtendDTO.builder()
                .eventName("乘客注册成功")
                .keys(inviteEvent.getPassengerId().toString())
                .messageType(MqMessageTypeEnum.SYNC)
                .topic(MemberRocketMQConstant.XX_PASSENGER_REGISTER_TOPIC_KEY)
                .sentTimeout(2000L)
                .build();
    }

    @Override
    public SendResult sendMessage(PassengerRegisterEvent registerEvent) {
        return MQMessageUtil.sendMessage(registerEvent, this::buildBaseSendExtendParam);
    }
}
