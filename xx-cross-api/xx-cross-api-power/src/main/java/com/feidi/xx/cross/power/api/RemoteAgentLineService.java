package com.feidi.xx.cross.power.api;

import com.feidi.xx.cross.power.api.domain.agent.vo.RemoteAgentLineVo;
import com.feidi.xx.cross.power.api.domain.line.bo.RemoteLineAgentBo;

import java.util.List;

/**
 * 线路服务
 *
 * <AUTHOR>
 * @date 2024/9/4
 */
public interface RemoteAgentLineService {

    /**
     * 匹配线路
     *
     * @param startAdCode 开始地区编码
     * @param endAdCode   结束地区编码
     * @return 线路信息
     */
    RemoteAgentLineVo matchLine(String startAdCode, String endAdCode);


    /**
     * 根据代理商ID获取线路
     * @param agentIds
     * @return
     */
    List<RemoteAgentLineVo> getLineByAgentIds(List<Long> agentIds);

    /**
     * 根据代理商id和线路id获取线路
     * @param agentId
     * @param  lineId
     * @return
     */
    RemoteAgentLineVo getAgentLineByAgentIdAndLineId(Long agentId,Long lineId);

    /**
     * 获取代理下的线路
     *
     * @param agentId 代理ID
     * @return 线路
     */
    List<Long> getAgentLine(Long agentId);

    /**
     * 获取线路下的代理
     *
     * @param lineId 线路ID
     * @return 代理ID
     */
    List<Long> getLineAgent(Long lineId);

    /**
     * 给线路分配代理商
     * @param bo
     * @return
     */
    boolean assignAgent(RemoteLineAgentBo bo);

    /**
     * 获取父代理下的线路
     *
     * @param parentId 代理ID
     * @return 线路
     */
    List<RemoteAgentLineVo> listByParentId(Long parentId);

}
