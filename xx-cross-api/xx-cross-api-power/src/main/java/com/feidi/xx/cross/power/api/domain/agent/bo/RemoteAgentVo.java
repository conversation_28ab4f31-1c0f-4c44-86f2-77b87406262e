package com.feidi.xx.cross.power.api.domain.agent.bo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  代理信息
 *
 * <AUTHOR>
 */
@Data
public class RemoteAgentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;


    /**
     * 上级代理商ID
     */
    private Long parentId;

    /**
     * 营业执照
     */
    private String license;

    /**
     * 名称
     */
    private String companyName;

    /**
     * 税号[组织代码]
     */
    private String taxNo;

    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 法人身份证号
     */
    private String carNo;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 支行
     */
    private String subBank;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 省
     */
    private Long provinceId;

    /**
     * 市
     */
    private Long cityId;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区
     */
    private Long districtId;

    /**
     * 地址
     */
    private String address;

    /**
     * 状态[StatusEnum]
     */
    private String status;

    /**
     * 保证金
     */
    private Long earnest;

    /**
     * 客服电话
     */
    private String servicesPhone;

    /**
     * 技术服务费比例
     */
    private BigDecimal technicalFeeRatio;

    /**
     * 是否展示运营城市的全部订单[IsYesEnum]
     */
    private String isShowCityOrder;

    /**
     * 开启虚拟电话[IsYesEnum]
     */
    private String axb;

    /**
     * 使用外省虚拟电话[IsYesEnum]
     */
    private String useAllAxb;
}
